# Replit Deployment Guide for ShopEasly Admin

## 🚀 Deploying ShopEasly Admin on Replit

This guide shows you how to deploy your ShopEasly Admin application on Replit and integrate it with your shopping website.

## 📋 Prerequisites

- Replit account (free or paid)
- Your shopping website ready for integration
- Basic knowledge of environment variables

## 🔧 Replit Setup Steps

### Step 1: Create New Repl

1. Go to [replit.com](https://replit.com)
2. Click "Create Repl"
3. Choose "Import from GitHub" or "Node.js"
4. Name your repl: `shopeasly-admin`
5. Set visibility to "Private" (recommended)

### Step 2: Upload Your Code

If importing from GitHub:
```bash
# Your GitHub repository URL
https://github.com/yourusername/shop-easly-admin
```

If uploading manually:
- Upload all files from your local project
- Ensure `package.json`, `app.js`, and all folders are included

### Step 3: Configure Environment Variables

In Replit, go to the "Secrets" tab (🔒 icon) and add:

```bash
# Required Variables
NODE_ENV=production
PORT=3000

# API Configuration
SHOPPING_SITE_API_KEY=your-secure-api-key-here
MOCK_API_URL=https://681a3a3c1ac1155635084e35.mockapi.io

# CORS Configuration (your Replit domain)
PRODUCTION_ORIGIN=https://[your-username]-shopeasly-admin.replit.app
FRONTEND_URL=https://[your-username]-shopeasly-admin.replit.app

# Optional: Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Optional: SMS Configuration
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=+**********

# Optional: AI Integration
OPENAI_API_KEY=your_openai_key

# Optional: Canva Integration
CANVA_CLIENT_ID=your_canva_client_id
CANVA_CLIENT_SECRET=your_canva_client_secret
CANVA_REDIRECT_URI=https://[your-username]-shopeasly-admin.replit.app/api/canva/callback
```

### Step 4: Install Dependencies

In the Replit Shell, run:
```bash
npm install
```

### Step 5: Start the Application

Click the "Run" button or use:
```bash
npm start
```

Your app will be available at:
```
https://[your-username]-shopeasly-admin.replit.app
```

## 🔗 Shopping Website Integration

### Update Your Integration Code

Replace the base URL in your shopping website integration:

```javascript
// OLD (Render example)
window.shopEasly = new ShopEaslyAPI({
  apiKey: 'your-api-key-here',
  baseUrl: 'https://shop-easly-admin.onrender.com',
  debug: true
});

// NEW (Replit)
window.shopEasly = new ShopEaslyAPI({
  apiKey: 'your-api-key-here',
  baseUrl: 'https://[your-username]-shopeasly-admin.replit.app',
  debug: true
});
```

### Example Integration URLs

Replace `[your-username]` with your actual Replit username:

```javascript
// Stock checking
const stockCheck = await fetch(
  'https://[your-username]-shopeasly-admin.replit.app/api/production/inventory/check/PRODUCT_ID',
  {
    headers: { 'X-API-Key': 'your-api-key-here' }
  }
);

// Order submission
const orderSubmit = await fetch(
  'https://[your-username]-shopeasly-admin.replit.app/api/production/orders',
  {
    method: 'POST',
    headers: {
      'X-API-Key': 'your-api-key-here',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(orderData)
  }
);
```

## 🌐 Domain Configuration

### Your Replit Domain

Your application will be accessible at:
```
https://[your-username]-shopeasly-admin.replit.app
```

### Custom Domain (Replit Hacker Plan)

If you have Replit Hacker plan, you can use a custom domain:

1. Go to your repl settings
2. Click "Custom Domain"
3. Enter your domain: `admin.yourstore.com`
4. Update your integration code with the custom domain

## 🔒 Security Configuration

### API Key Security

1. **Never expose your API key** in client-side code
2. **Use environment variables** in Replit Secrets
3. **Rotate keys regularly** for production

### CORS Security

The application is configured to allow:
- ✅ Your Replit domain (`.replit.app`)
- ✅ Custom domains you configure
- ✅ Localhost for development
- ❌ All other domains (blocked)

## 📊 Testing Your Deployment

### 1. Test the Admin Interface

Visit your Replit URL:
```
https://[your-username]-shopeasly-admin.replit.app
```

You should see the ShopEasly Admin dashboard.

### 2. Test API Endpoints

```bash
# Health check (no auth required)
curl https://[your-username]-shopeasly-admin.replit.app/api/production/health

# Inventory check (requires API key)
curl -H "X-API-Key: your-api-key" \
     https://[your-username]-shopeasly-admin.replit.app/api/production/inventory/check/TEST_PRODUCT
```

### 3. Test Integration

Use the integration tester:
```
https://[your-username]-shopeasly-admin.replit.app/shopping-site-integration-test.html
```

## 🚨 Common Replit Issues & Solutions

### Issue 1: App Goes to Sleep
**Problem:** Free Replit apps sleep after inactivity
**Solution:** 
- Upgrade to Replit Hacker plan for always-on
- Use a ping service to keep it awake
- Consider this for production use

### Issue 2: Environment Variables Not Loading
**Problem:** Secrets not accessible
**Solution:**
- Ensure variables are in "Secrets" tab, not "Environment"
- Restart the repl after adding secrets
- Check variable names match exactly

### Issue 3: CORS Errors
**Problem:** Shopping website can't connect
**Solution:**
- Verify your Replit domain in CORS settings
- Check the exact domain format
- Ensure HTTPS is used

### Issue 4: File Upload Issues
**Problem:** Large files or many files
**Solution:**
- Use GitHub import instead of manual upload
- Check file size limits
- Ensure all dependencies are in package.json

## 🔄 Keeping Your App Running

### For Free Replit Users:
```javascript
// Add this to keep your app awake (basic ping)
setInterval(() => {
  fetch('https://[your-username]-shopeasly-admin.replit.app/api/production/health')
    .catch(() => {}); // Ignore errors
}, 5 * 60 * 1000); // Every 5 minutes
```

### For Paid Replit Users:
- Enable "Always On" in repl settings
- Your app will run 24/7 without interruption

## 📈 Monitoring Your Replit Deployment

### Check Logs
In Replit console, monitor:
```bash
# View recent logs
tail -f ~/.local/share/replit/logs/repl.log

# Check for errors
grep -i error ~/.local/share/replit/logs/repl.log
```

### Performance Monitoring
```javascript
// Add to your shopping website
const startTime = Date.now();
const response = await shopEasly.checkStock('PRODUCT_ID');
const responseTime = Date.now() - startTime;

console.log(`API Response Time: ${responseTime}ms`);
```

## 🎯 Production Checklist

Before going live with Replit:

- ✅ All environment variables configured
- ✅ API key is secure and not exposed
- ✅ CORS settings allow your shopping website
- ✅ All endpoints tested and working
- ✅ Error handling implemented in shopping website
- ✅ Monitoring/logging set up
- ✅ Backup plan if Replit goes down
- ✅ Consider upgrading to paid plan for reliability

## 🔄 Alternative Deployment Options

If Replit doesn't meet your needs:

1. **Render** - Better for production, auto-scaling
2. **Heroku** - Enterprise features, more expensive
3. **Vercel** - Great for static sites with API routes
4. **Railway** - Simple deployment, good pricing
5. **DigitalOcean App Platform** - Full control, scalable

## 🎉 Success!

Once deployed, your ShopEasly Admin will:
- ✅ Receive orders from your shopping website
- ✅ Manage inventory in real-time
- ✅ Send customer notifications
- ✅ Provide comprehensive order management
- ✅ Generate customer analytics

Your integration URL:
```
https://[your-username]-shopeasly-admin.replit.app
```

Remember to replace `[your-username]` with your actual Replit username!
