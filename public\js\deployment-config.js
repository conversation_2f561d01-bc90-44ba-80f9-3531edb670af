/**
 * ShopEasly Deployment Configuration Helper
 * Automatically detects and configures for different hosting platforms
 */

class DeploymentConfig {
  constructor() {
    this.platform = this.detectPlatform();
    this.config = this.getConfig();
  }

  detectPlatform() {
    const hostname = window.location.hostname;
    
    if (hostname.includes('replit.app') || hostname.includes('repl.co')) {
      return 'replit';
    } else if (hostname.includes('onrender.com')) {
      return 'render';
    } else if (hostname.includes('herokuapp.com')) {
      return 'heroku';
    } else if (hostname.includes('vercel.app')) {
      return 'vercel';
    } else if (hostname.includes('netlify.app')) {
      return 'netlify';
    } else if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return 'local';
    } else {
      return 'custom';
    }
  }

  getConfig() {
    const configs = {
      replit: {
        name: 'Replit',
        baseUrl: window.location.origin,
        features: {
          alwaysOn: false, // Free tier sleeps
          customDomain: true, // With paid plan
          ssl: true,
          scaling: false
        },
        notes: [
          'Free tier apps sleep after inactivity',
          'Upgrade to Hacker plan for always-on',
          'Custom domains available with paid plan'
        ]
      },
      render: {
        name: 'Render',
        baseUrl: window.location.origin,
        features: {
          alwaysOn: true,
          customDomain: true,
          ssl: true,
          scaling: true
        },
        notes: [
          'Free tier has some limitations',
          'Automatic SSL certificates',
          'Good for production use'
        ]
      },
      heroku: {
        name: 'Heroku',
        baseUrl: window.location.origin,
        features: {
          alwaysOn: false, // Free tier sleeps
          customDomain: true,
          ssl: true,
          scaling: true
        },
        notes: [
          'Free tier discontinued',
          'Paid plans start at $7/month',
          'Enterprise-grade features'
        ]
      },
      local: {
        name: 'Local Development',
        baseUrl: 'http://localhost:3000',
        features: {
          alwaysOn: true,
          customDomain: false,
          ssl: false,
          scaling: false
        },
        notes: [
          'For development only',
          'Not accessible from internet',
          'Use ngrok for external testing'
        ]
      },
      custom: {
        name: 'Custom Domain',
        baseUrl: window.location.origin,
        features: {
          alwaysOn: true,
          customDomain: true,
          ssl: true,
          scaling: true
        },
        notes: [
          'Custom deployment detected',
          'Verify SSL certificate',
          'Check CORS configuration'
        ]
      }
    };

    return configs[this.platform] || configs.custom;
  }

  getIntegrationCode(apiKey = 'your-api-key-here') {
    return {
      basic: `// ShopEasly Integration for ${this.config.name}
window.shopEasly = new ShopEaslyAPI({
  apiKey: '${apiKey}',
  baseUrl: '${this.config.baseUrl}',
  debug: true
});`,

      stockCheck: `// Check product stock
const stockCheck = await shopEasly.checkStock('PRODUCT_ID', 2);
if (!stockCheck.inStock) {
  alert('Sorry, this item is out of stock');
}`,

      cartValidation: `// Validate shopping cart
const cartValidation = await shopEasly.validateCart(cartItems);
if (!cartValidation.canFulfillOrder) {
  showOutOfStockItems(cartValidation.products);
}`,

      orderSubmission: `// Submit order after payment
const orderResult = await shopEasly.submitOrder({
  orderId: 'WEB_' + Date.now(),
  customer: {
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '************',
    address: '123 Main St, City, State 12345'
  },
  items: cartItems,
  totalPrice: 99.99,
  paymentStatus: 'completed'
});

if (orderResult.success) {
  showOrderConfirmation(orderResult.orderId);
}`
    };
  }

  getEnvironmentVariables() {
    const baseVars = {
      'NODE_ENV': 'production',
      'PORT': '3000',
      'SHOPPING_SITE_API_KEY': 'your-secure-api-key-here',
      'MOCK_API_URL': 'https://681a3a3c1ac1155635084e35.mockapi.io'
    };

    const platformVars = {
      replit: {
        'PRODUCTION_ORIGIN': `${this.config.baseUrl}`,
        'FRONTEND_URL': `${this.config.baseUrl}`
      },
      render: {
        'PRODUCTION_ORIGIN': `${this.config.baseUrl}`,
        'FRONTEND_URL': `${this.config.baseUrl}`
      },
      local: {
        'PRODUCTION_ORIGIN': 'http://localhost:3000',
        'FRONTEND_URL': 'http://localhost:3000'
      }
    };

    return {
      ...baseVars,
      ...(platformVars[this.platform] || platformVars.local)
    };
  }

  displayInfo() {
    return {
      platform: this.config.name,
      baseUrl: this.config.baseUrl,
      features: this.config.features,
      notes: this.config.notes,
      environmentVariables: this.getEnvironmentVariables(),
      integrationCode: this.getIntegrationCode()
    };
  }

  // Helper method to generate platform-specific deployment instructions
  getDeploymentInstructions() {
    const instructions = {
      replit: [
        '1. Create new Node.js repl on replit.com',
        '2. Upload your ShopEasly Admin code',
        '3. Add environment variables in Secrets tab',
        '4. Click Run to start the application',
        '5. Your app will be at: https://[username]-[repl-name].replit.app'
      ],
      render: [
        '1. Connect your GitHub repository to Render',
        '2. Create new Web Service',
        '3. Set build command: npm install',
        '4. Set start command: npm start',
        '5. Add environment variables in dashboard'
      ],
      local: [
        '1. Clone the repository locally',
        '2. Run: npm install',
        '3. Create .env file with environment variables',
        '4. Run: npm start',
        '5. Access at: http://localhost:3000'
      ]
    };

    return instructions[this.platform] || instructions.local;
  }
}

// Auto-initialize when script loads
const deploymentConfig = new DeploymentConfig();

// Make available globally
window.DeploymentConfig = DeploymentConfig;
window.deploymentConfig = deploymentConfig;

// Display platform info in console
console.log('🚀 ShopEasly Deployment Info:', deploymentConfig.displayInfo());

// Helper function to copy integration code to clipboard
window.copyIntegrationCode = function(type = 'basic') {
  const code = deploymentConfig.getIntegrationCode()[type];
  navigator.clipboard.writeText(code).then(() => {
    console.log('✅ Integration code copied to clipboard!');
    // Show toast notification if available
    if (window.showNotification) {
      showNotification('Integration code copied to clipboard!', 'success');
    }
  }).catch(err => {
    console.error('❌ Failed to copy code:', err);
  });
};

// Helper function to show platform-specific setup guide
window.showSetupGuide = function() {
  const info = deploymentConfig.displayInfo();
  const instructions = deploymentConfig.getDeploymentInstructions();
  
  console.group(`📋 ${info.platform} Setup Guide`);
  console.log('🔗 Base URL:', info.baseUrl);
  console.log('✨ Features:', info.features);
  console.log('📝 Notes:', info.notes);
  console.log('🔧 Deployment Steps:', instructions);
  console.log('🌍 Environment Variables:', info.environmentVariables);
  console.groupEnd();
  
  return {
    platform: info.platform,
    baseUrl: info.baseUrl,
    instructions: instructions,
    environmentVariables: info.environmentVariables
  };
};

// Auto-detect and show setup info on page load
document.addEventListener('DOMContentLoaded', function() {
  // Update any deployment-specific UI elements
  const platformElements = document.querySelectorAll('[data-platform-url]');
  platformElements.forEach(element => {
    element.textContent = deploymentConfig.config.baseUrl;
  });

  // Update form fields with detected URL
  const urlInputs = document.querySelectorAll('input[id*="url"], input[id*="Url"]');
  urlInputs.forEach(input => {
    if (input.value.includes('[your-') || input.value === '') {
      input.value = deploymentConfig.config.baseUrl;
    }
  });

  // Show platform info banner if element exists
  const platformBanner = document.getElementById('platformBanner');
  if (platformBanner) {
    const info = deploymentConfig.displayInfo();
    platformBanner.innerHTML = `
      <div class="alert alert-info">
        <strong>🚀 Detected Platform: ${info.platform}</strong><br>
        Base URL: <code>${info.baseUrl}</code><br>
        <small>${info.notes[0]}</small>
      </div>
    `;
  }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DeploymentConfig;
}
