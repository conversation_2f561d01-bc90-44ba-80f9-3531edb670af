:root {
  --primary-color: #4f46e5;
  --primary-light: #c7d2fe;
  --primary-dark: #4338ca;
  --secondary-color: #ff6f61;
  --text-dark: #1f2937;
  --text-light: #6b7280;
  --bg-light: #f9fafb;
  --bg-white: #ffffff;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --transition: all 0.3s ease;
}

/* Modern Glassmorphism AI Assistant <PERSON><PERSON> */

body, html {
  min-height: 100vh;
  background: linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
  color: var(--text-dark);
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.container {
  max-width: 1200px;
  margin: 40px auto 0 auto;
  padding: 0 24px 40px 24px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255,255,255,0.15);
  box-shadow: 0 8px 32px 0 rgba(31,38,135,0.18);
  backdrop-filter: blur(8px);
  border-radius: 24px;
  padding: 18px 32px;
  margin-bottom: 0;
}

.logo {
  display: flex;
  align-items: center;
  gap: 14px;
  font-size: 1.5rem;
  font-weight: 700;
  color: #222;
}
.logo-icon {
  background: linear-gradient(135deg, #6e8efb 0%, #a777e3 100%);
  color: #fff;
  border-radius: 50%;
  padding: 10px;
  font-size: 1.3rem;
  box-shadow: 0 2px 8px rgba(110,142,251,0.15);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 18px;
}
.model-selector {
  display: flex;
  gap: 8px;
  background: rgba(255,255,255,0.18);
  border-radius: 12px;
  padding: 4px 8px;
  box-shadow: 0 2px 8px rgba(110,142,251,0.08);
}
.model-option {
  padding: 6px 14px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  color: #555;
  transition: background 0.2s, color 0.2s;
}
.model-option.active, .model-option:hover {
  background: linear-gradient(135deg, #6e8efb 0%, #a777e3 100%);
  color: #fff;
}
.user-avatar {
  background: linear-gradient(135deg, #a777e3 0%, #6e8efb 100%);
  color: #fff;
  border-radius: 50%;
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.1rem;
  box-shadow: 0 2px 8px rgba(110,142,251,0.15);
}
.settings-btn {
  background: none;
  border: none;
  color: #6e8efb;
  font-size: 1.3rem;
  cursor: pointer;
  transition: color 0.2s;
}
.settings-btn:hover {
  color: #a777e3;
}

.main-content {
  display: flex;
  gap: 32px;
}

.sidebar {
  width: 270px;
  background: rgba(255,255,255,0.18);
  border-radius: 20px;
  box-shadow: 0 4px 24px 0 rgba(110,142,251,0.10);
  padding: 24px 18px 18px 18px;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.sidebar-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #6e8efb;
}
.conversation-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.conversation-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 8px;
  border-radius: 10px;
  cursor: pointer;
  transition: background 0.18s;
  background: transparent;
}
.conversation-item.active, .conversation-item:hover {
  background: linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);
}
.conversation-icon {
  background: linear-gradient(135deg, #6e8efb 0%, #a777e3 100%);
  color: #fff;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
}
.conversation-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.conversation-name {
  font-weight: 600;
  color: #333;
  font-size: 1rem;
}
.conversation-preview {
  font-size: 0.92rem;
  color: #888;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}
.new-chat-btn {
  margin-top: 10px;
  background: linear-gradient(135deg, #6e8efb 0%, #a777e3 100%);
  color: #fff;
  border: none;
  border-radius: 10px;
  padding: 10px 0;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.new-chat-btn:hover {
  background: linear-gradient(135deg, #a777e3 0%, #6e8efb 100%);
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255,255,255,0.22);
  border-radius: 24px;
  box-shadow: 0 8px 32px 0 rgba(31,38,135,0.10);
  min-width: 0;
  min-height: 600px;
  max-height: 800px;
  overflow: hidden;
}
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 28px 12px 28px;
  border-bottom: 1px solid rgba(110,142,251,0.10);
  background: transparent;
}
.chat-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  color: #6e8efb;
}
.chat-actions {
  display: flex;
  gap: 10px;
}
.action-btn {
  background: none;
  border: none;
  color: #6e8efb;
  font-size: 1.1rem;
  cursor: pointer;
  border-radius: 50%;
  padding: 6px;
  transition: background 0.18s, color 0.18s;
}
.action-btn:hover {
  background: #e0e7ff;
  color: #a777e3;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 28px 32px 18px 32px;
  display: flex;
  flex-direction: column;
  gap: 18px;
  background: transparent;
}
.message {
  display: flex;
  align-items: flex-end;
  gap: 14px;
  margin-bottom: 0;
}
.message-avatar {
  background: linear-gradient(135deg, #6e8efb 0%, #a777e3 100%);
  color: #fff;
  border-radius: 50%;
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  box-shadow: 0 2px 8px rgba(110,142,251,0.10);
}
.message-content {
  position: relative;
  background: rgba(255,255,255,0.65);
  border-radius: 16px 16px 16px 4px;
  padding: 14px 18px 10px 18px;
  box-shadow: 0 2px 8px rgba(110,142,251,0.08);
  min-width: 120px;
  max-width: 520px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.message-bubble-pointer {
  position: absolute;
  left: -10px;
  top: 18px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 10px solid rgba(255,255,255,0.65);
}
.message-text {
  font-size: 1.05rem;
  color: #222;
  word-break: break-word;
}
.message-time {
  font-size: 0.85rem;
  color: #888;
  align-self: flex-end;
  margin-top: 2px;
}

/* User message (right aligned) */
.message.user {
  flex-direction: row-reverse;
}
.message.user .message-content {
  background: linear-gradient(135deg, #6e8efb 0%, #a777e3 100%);
  color: #fff;
  border-radius: 16px 16px 4px 16px;
}
.message.user .message-bubble-pointer {
  left: auto;
  right: -10px;
  border-right: none;
  border-left: 10px solid #a777e3;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
}
.message.user .message-time {
  color: #e0e7ff;
}

.chat-input-container {
  padding: 18px 28px 18px 28px;
  background: rgba(255,255,255,0.18);
  border-top: 1px solid rgba(110,142,251,0.10);
}
.chat-input-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(255,255,255,0.65);
  border-radius: 14px;
  box-shadow: 0 2px 8px rgba(110,142,251,0.08);
  padding: 8px 16px;
}
.chat-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 1.08rem;
  color: #222;
  padding: 8px 0;
}
.input-action-btn {
  background: none;
  border: none;
  color: #6e8efb;
  font-size: 1.2rem;
  cursor: pointer;
  border-radius: 50%;
  padding: 6px;
  transition: background 0.18s, color 0.18s;
}
.input-action-btn.send-btn {
  background: linear-gradient(135deg, #6e8efb 0%, #a777e3 100%);
  color: #fff;
  box-shadow: 0 2px 8px rgba(110,142,251,0.10);
}
.input-action-btn.send-btn:hover {
  background: linear-gradient(135deg, #a777e3 0%, #6e8efb 100%);
}
.input-action-btn:hover {
  background: #e0e7ff;
  color: #a777e3;
}

.capabilities {
  display: flex;
  gap: 24px;
  margin-top: 18px;
  justify-content: center;
}
.capability-card {
  background: rgba(255,255,255,0.18);
  border-radius: 18px;
  box-shadow: 0 4px 24px 0 rgba(110,142,251,0.10);
  padding: 24px 18px 18px 18px;
  min-width: 220px;
  max-width: 260px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  text-align: center;
  transition: box-shadow 0.18s, transform 0.18s;
}
.capability-card:hover {
  box-shadow: 0 8px 32px 0 rgba(110,142,251,0.18);
  transform: translateY(-4px) scale(1.03);
}
.capability-icon {
  font-size: 1.7rem;
  background: linear-gradient(135deg, #6e8efb 0%, #a777e3 100%);
  color: #fff;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}
.capability-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #6e8efb;
}
.capability-description {
  font-size: 0.98rem;
  color: #555;
}

/* Responsive Design */
@media (max-width: 1100px) {
  .main-content {
    flex-direction: column;
  }
  .sidebar {
    width: 100%;
    flex-direction: row;
    gap: 18px;
    margin-bottom: 18px;
  }
  .chat-container {
    min-height: 400px;
    max-height: 600px;
  }
}
@media (max-width: 800px) {
  .container {
    padding: 0 6px 24px 6px;
  }
  .header {
    flex-direction: column;
    gap: 12px;
    padding: 12px 10px;
  }
  .main-content {
    gap: 12px;
  }
  .sidebar {
    padding: 12px 6px 8px 6px;
    border-radius: 12px;
  }
  .chat-container {
    border-radius: 12px;
    min-height: 300px;
    max-height: 400px;
  }
  .chat-header, .chat-input-container {
    padding: 10px 8px;
  }
  .chat-messages {
    padding: 10px 8px 8px 8px;
  }
  .capabilities {
    flex-direction: column;
    gap: 12px;
  }
  .capability-card {
    min-width: 0;
    max-width: 100%;
    padding: 14px 8px 10px 8px;
  }
}

/* Hide scrollbars for chat-messages */
.chat-messages::-webkit-scrollbar {
  width: 0.4em;
  background: transparent;
}
.chat-messages::-webkit-scrollbar-thumb {
  background: #e0e7ff;
  border-radius: 8px;
}

/* Utility classes */
.glass {
  background: rgba(255,255,255,0.18);
  box-shadow: 0 4px 24px 0 rgba(110,142,251,0.10);
  backdrop-filter: blur(8px);
  border-radius: 18px;
}

/* Status indicator pulse animation */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Enhanced chat input styling */
.chat-input-form input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.chat-input-form button:hover {
  background: #5b21b6;
  transform: translateY(-1px);
}

/* Action button hover effects */
.action-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  color: #5b21b6;
}

/* POD shortcut enhancements */
.pod-shortcut-item:hover {
  background: rgba(99, 102, 241, 0.1) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* Chat history scrollbar styling */
.chat-history::-webkit-scrollbar {
  width: 6px;
}

.chat-history::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.chat-history::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.chat-history::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .ai-helper-main-layout {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .ai-sidebar {
    min-width: auto !important;
    width: 100% !important;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .user-menu {
    justify-content: center;
    flex-wrap: wrap;
  }

  .status-indicator {
    order: -1;
  }
}