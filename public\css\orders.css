/* Order Management Dashboard - Canva Design Implementation */

/* Dashboard Container */
.dashboard-container {
  padding: 1.5rem;
  max-width: 100%;
  margin: 0 auto;
}

/* Clean, modern design with gradients and shadows */
.card {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  background: #ffffff;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1px solid #e9ecef;
  border-radius: 12px 12px 0 0;
  padding: 1rem 1.5rem;
}

/* Analytics Dashboard */
.analytics-dashboard {
  padding: 1rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  margin-bottom: 1rem;
}

.stat-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  height: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.12);
}

/* Metrics */
.metric-value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Tools Panel */
.tools-panel {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 1.5rem;
  height: 100%;
}

.quick-stats {
  background: #ffffff;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

/* Table and Layout Improvements */
.table-responsive {
  border-radius: 0.375rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  background: white;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  margin: 0 1px;
  transition: all 0.2s ease;
}

.btn-group-sm .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Page Header Improvements */
.page-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid #e9ecef;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Order Statistics */
.design-stats {
  background: white;
  padding: 1rem;
  border-radius: 0.375rem;
  border: 1px solid #e9ecef;
  margin-top: 1rem;
}

/* Table Styling */
.table {
  font-size: 0.9rem;
}

.table thead th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  padding: 1rem 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.8rem;
}

.table tbody tr {
  transition: all 0.3s ease;
  border-bottom: 1px solid #f1f3f4;
}

.table tbody tr:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.table tbody td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
  border-bottom: 1px solid #f1f3f4;
}

/* Status Badges */
.badge {
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Timeline Styles for Tracking Modal */
.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e9ecef;
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
}

.timeline-marker {
  position: absolute;
  left: -2rem;
  top: 0.25rem;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-content {
  padding-left: 1rem;
}

.timeline-content h6 {
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.timeline-content p {
  margin-bottom: 0.25rem;
}

/* Status Badge Improvements */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Notification Improvements */
.alert {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .canva-embed-wrapper {
    height: 250px !important;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }

  .header-actions .btn,
  .header-actions .dropdown {
    width: 100%;
  }

  .header-actions .dropdown .btn {
    width: 100%;
  }

  .page-header {
    padding: 1rem;
  }

  .design-panel {
    margin-top: 1rem;
  }

  /* Mobile dropdown styling */
  .dropdown-menu {
    width: 100%;
    max-width: none;
  }

  .dropdown-toggle {
    width: 100%;
    text-align: left;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .table th,
  .table td {
    padding: 0.5rem;
  }

  /* Hide some table columns on mobile */
  .table th:nth-child(3),
  .table td:nth-child(3),
  .table th:nth-child(6),
  .table td:nth-child(6) {
    display: none;
  }
}

/* Loading Animation */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

.loading .spinner-border {
  width: 3rem;
  height: 3rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }

  .page-header {
    flex-direction: column;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .analytics-dashboard {
    padding: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .metric-value {
    font-size: 1.5rem;
  }

  .tools-panel {
    padding: 1rem;
    margin-top: 1rem;
  }

  /* Hide some table columns on mobile */
  .d-none.d-md-table-cell {
    display: none !important;
  }

  .table {
    font-size: 0.8rem;
  }

  .table thead th,
  .table tbody td {
    padding: 0.5rem 0.25rem;
  }
}

@media (min-width: 992px) {
  .dashboard-container {
    padding: 2rem;
  }

  .card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0,0,0,0.15);
  }

  .stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(0,0,0,0.15);
  }
}

/* Notification Toasts */
.toast-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;
}

.toast {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Additional Order Management Specific Styles */
.order-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.order-filters select {
  min-width: 150px;
}

/* Action Dropdown Styling */
.dropdown-toggle {
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;
}

.dropdown-toggle:hover {
  background-color: #f8f9fa;
  border-color: #007bff;
}

.dropdown-menu {
  border: none;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border-radius: 0.5rem;
  padding: 0.5rem 0;
  min-width: 160px;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border-radius: 0;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.dropdown-item.text-danger:hover {
  background-color: #f8d7da;
  color: #721c24;
}

.dropdown-item i {
  width: 16px;
  text-align: center;
}

.dropdown-divider {
  margin: 0.5rem 0;
  border-color: #e9ecef;
}

/* Action button container */
.order-actions {
  display: flex;
  gap: 0.25rem;
  justify-content: center;
}

.order-actions .btn {
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

/* Status-specific styling */
.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-processing {
  background-color: #d1ecf1;
  color: #0c5460;
}

.status-shipped {
  background-color: #cce5ff;
  color: #004085;
}

.status-delivered,
.status-completed {
  background-color: #d4edda;
  color: #155724;
}

.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

/* Customer info styling */
.customer-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.customer-name {
  font-weight: 600;
  color: #495057;
}

.customer-address {
  font-size: 0.875rem;
  color: #6c757d;
}

/* Price styling */
.order-price {
  font-weight: 600;
  color: #28a745;
  font-size: 1rem;
}

/* Date styling */
.order-date {
  font-size: 0.875rem;
  color: #6c757d;
}

/* Empty state styling */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state p {
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.empty-state a {
  color: #007bff;
  text-decoration: none;
  font-weight: 600;
}

.empty-state a:hover {
  text-decoration: underline;
}

/* Modal Styles for Shipping Windows */
.shipping-modal {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.shipping-modal .modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.shipping-modal .modal-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  border-bottom: none;
}

.shipping-modal .modal-header .btn-close {
  filter: invert(1);
}

.fedex-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.tracking-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #28a745;
}

.status-indicator.pending {
  background: #ffc107;
}

.status-indicator.shipped {
  background: #007bff;
}

.status-indicator.delivered {
  background: #28a745;
}

/* Items List Styling */
.items-list {
  max-width: 250px;
  font-size: 0.875rem;
}

.item-entry {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  padding: 0.25rem 0;
  border-bottom: 1px solid #f1f3f4;
}

.item-entry:last-child {
  border-bottom: none;
}

.item-entry .fw-semibold {
  font-size: 0.875rem;
  line-height: 1.2;
  color: #495057;
}

.item-entry .text-muted {
  font-size: 0.75rem;
  color: #6c757d;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.items-list .text-muted.small {
  font-style: italic;
  color: #dc3545;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Responsive adjustments for items column */
@media (max-width: 768px) {
  .items-list {
    max-width: 200px;
    font-size: 0.8rem;
  }

  .item-entry .fw-semibold {
    font-size: 0.8rem;
  }

  .item-entry .text-muted {
    font-size: 0.7rem;
  }
}

@media (max-width: 576px) {
  .items-list {
    max-width: 150px;
    font-size: 0.75rem;
  }

  .item-entry {
    padding: 0.125rem 0;
  }

  .item-entry .fw-semibold {
    font-size: 0.75rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .item-entry .text-muted {
    font-size: 0.65rem;
  }
}

/* Small items list for customer profile modal */
.items-list-small {
  max-width: 150px;
  font-size: 0.75rem;
}

.items-list-small .small {
  font-size: 0.7rem;
  line-height: 1.2;
  margin-bottom: 0.125rem;
}

.items-list-small .small:last-child {
  margin-bottom: 0;
}

/* View Items Button Styling */
.view-items-btn {
  font-size: 0.8rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.view-items-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.view-items-btn .badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.4rem;
}

/* Order Items Modal Styling */
#orderItemsModal .modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

#orderItemsModal .modal-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  border-bottom: none;
}

#orderItemsModal .modal-header .btn-close {
  filter: invert(1);
}

#orderItemsModal .card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.2s ease;
}

#orderItemsModal .card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

#orderItemsModal .card-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #495057;
}

#orderItemsModal .customer-summary div,
#orderItemsModal .order-summary div {
  font-size: 0.9rem;
  line-height: 1.4;
}

#orderItemsModal .table th {
  background: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #495057;
}

#orderItemsModal .table td {
  vertical-align: middle;
  font-size: 0.9rem;
  border-bottom: 1px solid #f1f3f4;
}

#orderItemsModal .item-details .fw-semibold {
  font-size: 0.9rem;
  color: #495057;
  line-height: 1.3;
}

#orderItemsModal .item-details small {
  font-size: 0.75rem;
  color: #6c757d;
}

/* Responsive adjustments for Order Items Modal */
@media (max-width: 768px) {
  #orderItemsModal .modal-dialog {
    margin: 0.5rem;
    max-width: calc(100% - 1rem);
  }

  #orderItemsModal .row {
    margin-bottom: 1rem;
  }

  #orderItemsModal .col-md-6 {
    margin-bottom: 1rem;
  }

  #orderItemsModal .table {
    font-size: 0.8rem;
  }

  #orderItemsModal .table th,
  #orderItemsModal .table td {
    padding: 0.5rem 0.25rem;
  }

  .view-items-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  .view-items-btn .badge {
    font-size: 0.65rem;
    padding: 0.125rem 0.25rem;
  }
}

@media (max-width: 576px) {
  #orderItemsModal .modal-header h5 {
    font-size: 1rem;
  }

  #orderItemsModal .card-title {
    font-size: 0.9rem;
  }

  #orderItemsModal .customer-summary div,
  #orderItemsModal .order-summary div {
    font-size: 0.8rem;
  }

  /* Stack table content on very small screens */
  #orderItemsModal .table-responsive {
    font-size: 0.75rem;
  }
}
