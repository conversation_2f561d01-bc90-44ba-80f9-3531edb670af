<main class="content-area">
<!-- Production Order Management Dashboard -->
<div class="container-fluid py-3">

  <!-- Header Section -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
      <i class="fas fa-shopping-cart me-2 text-primary"></i>Order Management
    </h2>
    <div class="d-flex gap-2">
      <button class="btn btn-outline-primary btn-sm" id="refreshBtn">
        <i class="fas fa-sync-alt me-1"></i>Refresh
      </button>
      <button class="btn btn-success btn-sm" id="exportBtn">
        <i class="fas fa-download me-1"></i>Export
      </button>
    </div>
  </div>

  <!-- Quick Stats Row -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card border-0 shadow-sm">
        <div class="card-body text-center">
          <div class="text-primary mb-2">
            <i class="fas fa-shopping-bag fa-2x"></i>
          </div>
          <h4 class="mb-1" id="totalOrdersCount"><%= orders ? orders.length : 0 %></h4>
          <small class="text-muted">Total Orders</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card border-0 shadow-sm">
        <div class="card-body text-center">
          <div class="text-warning mb-2">
            <i class="fas fa-clock fa-2x"></i>
          </div>
          <h4 class="mb-1" id="pendingOrdersCount">-</h4>
          <small class="text-muted">Pending Orders</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card border-0 shadow-sm">
        <div class="card-body text-center">
          <div class="text-info mb-2">
            <i class="fas fa-truck fa-2x"></i>
          </div>
          <h4 class="mb-1" id="shippedOrdersCount">-</h4>
          <small class="text-muted">Shipped Orders</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card border-0 shadow-sm">
        <div class="card-body text-center">
          <div class="text-success mb-2">
            <i class="fas fa-dollar-sign fa-2x"></i>
          </div>
          <h4 class="mb-1">$<span id="totalRevenue">-</span></h4>
          <small class="text-muted">Total Revenue</small>
        </div>
      </div>
    </div>
  </div>

  <!-- Orders Table -->
  <div class="card border-0 shadow-sm">
    <div class="card-header bg-white">
      <div class="d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
          <i class="fas fa-list me-2"></i>Orders
        </h5>
        <div class="d-flex gap-2">
          <input type="text" class="form-control form-control-sm" id="searchInput" placeholder="Search orders..." style="width: 200px;">
          <select class="form-select form-select-sm" id="statusFilter" onchange="filterOrders()" style="width: 140px;">
            <option value="">All Status</option>
            <option value="pending">Pending</option>
            <option value="processing">Processing</option>
            <option value="shipped">Shipped</option>
            <option value="delivered">Delivered</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>
    </div>
    <div class="table-responsive">
      <table class="table table-hover mb-0">
        <thead class="table-light">
          <tr>
            <th style="width: 80px;">Order ID</th>
            <th style="width: 160px;">Customer</th>
            <th style="width: 100px;" class="text-center">Items</th>
            <th style="width: 140px;" class="d-none d-md-table-cell">Email</th>
            <th style="width: 90px;">Amount</th>
            <th style="width: 100px;">Status</th>
            <th style="width: 120px;" class="d-none d-lg-table-cell">Date</th>
            <th style="width: 100px;" class="text-center">Actions</th>
          </tr>
        </thead>
        <tbody id="ordersTableBody">
        <% if (!orders || orders.length === 0) { %>
          <tr>
            <td colspan="8" class="text-center py-5">
              <div class="empty-state">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No orders found</h5>
                <p class="text-muted mb-0">Orders from your shopping site will appear here</p>
              </div>
            </td>
          </tr>
        <% } else { %>
          <% orders.forEach(order => { %>
            <tr data-order-id="<%= order.id %>" data-status="<%= order.status %>" class="align-middle">
              <td>
                <span class="fw-bold text-primary">#<%= order.id %></span>
              </td>
              <td>
                <div>
                  <div class="fw-semibold"><%= order.customerName || 'N/A' %></div>
                  <% if (order.customerAddress) { %>
                    <small class="text-muted"><%= order.customerAddress.substring(0, 25) %>...</small>
                  <% } %>
                </div>
              </td>
              <td class="text-center">
                <% if (order.items && order.items.length > 0) { %>
                  <button class="btn btn-outline-primary btn-sm view-items-btn"
                          data-order-id="<%= order.id %>"
                          data-bs-toggle="tooltip"
                          title="View <%= order.items.length %> item<%= order.items.length > 1 ? 's' : '' %>">
                    <i class="fas fa-list me-1"></i>View Items
                    <span class="badge bg-primary ms-1"><%= order.items.length %></span>
                  </button>
                <% } else { %>
                  <span class="text-muted small">
                    <i class="fas fa-exclamation-triangle me-1"></i>No items
                  </span>
                <% } %>
              </td>
              <td class="d-none d-md-table-cell">
                <% if (order.customerEmail) { %>
                  <a href="mailto:<%= order.customerEmail %>" class="text-decoration-none small">
                    <%= order.customerEmail.length > 20 ? order.customerEmail.substring(0, 20) + '...' : order.customerEmail %>
                  </a>
                <% } else { %>
                  <span class="text-muted small">N/A</span>
                <% } %>
              </td>
              <td>
                <span class="fw-bold text-success">$<%= parseFloat(order.totalPrice || 0).toFixed(2) %></span>
              </td>
              <td>
                <%
                  // Simple status text rendering
                  let statusText = 'Unknown';

                  if (order.status) {
                    const status = order.status.toString().trim();
                    // Handle special cases
                    if (status.toLowerCase() === 'on_hold' || status.toLowerCase() === 'on hold') {
                      statusText = 'On Hold';
                    } else {
                      // Capitalize first letter
                      statusText = status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
                    }
                  }
                %>
                <%= statusText %>
              </td>
              <td class="d-none d-lg-table-cell">
                <div class="small">
                  <div><%= new Date(order.createdAt).toLocaleDateString() %></div>
                  <div class="text-muted"><%= new Date(order.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %></div>
                </div>
              </td>
              <td class="text-center">
                <div class="dropdown">
                  <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-ellipsis-v"></i>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                      <a class="dropdown-item" href="#" data-action="update-status" data-order-id="<%= order.id %>">
                        <i class="fas fa-edit me-2 text-primary"></i>Update Status
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="#" data-action="shipping-label" data-order-id="<%= order.id %>">
                        <i class="fas fa-shipping-fast me-2 text-info"></i>Shipping Label
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="#" data-action="generate-invoice" data-order-id="<%= order.id %>">
                        <i class="fas fa-file-invoice me-2 text-primary"></i>Generate Invoice
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="#" data-action="track-shipment" data-order-id="<%= order.id %>">
                        <i class="fas fa-map-marker-alt me-2 text-warning"></i>Track Order
                      </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                      <a class="dropdown-item" href="#" data-action="customer-profile" data-order-id="<%= order.id %>">
                        <i class="fas fa-user me-2 text-success"></i>Customer Profile
                      </a>
                    </li>
                  </ul>
                </div>
              </td>
            </tr>
          <% }) %>
        <% } %>
      </tbody>
    </table>
  </div>
</div>

<!-- Loading indicator -->
<div id="loadingIndicator" class="text-center py-4" style="display: none;">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">Loading...</span>
  </div>
  <p class="mt-2 text-muted">Loading order data...</p>
</div>

<!-- Notification Container -->
<div id="notificationContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1055;"></div>

</div> <!-- End container-fluid -->

<!-- Modals Section -->

<!-- Order Status Update Modal -->
<div class="modal fade" id="statusUpdateModal" tabindex="-1" aria-labelledby="statusUpdateModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="statusUpdateModalLabel">
          <i class="fas fa-edit me-2"></i>Update Order Status
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="statusUpdateForm">
          <div class="mb-3">
            <label for="statusOrderId" class="form-label">Order ID</label>
            <input type="text" class="form-control" id="statusOrderId" readonly>
          </div>
          <div class="mb-3">
            <label for="currentStatus" class="form-label">Current Status</label>
            <input type="text" class="form-control" id="currentStatus" readonly>
          </div>
          <div class="mb-3">
            <label for="newStatus" class="form-label">New Status</label>
            <select class="form-select" id="newStatus" required>
              <option value="">Select new status...</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="statusNotes" class="form-label">Notes (Optional)</label>
            <textarea class="form-control" id="statusNotes" rows="3" placeholder="Add any notes about this status change..."></textarea>
          </div>
          <div class="mb-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="notifyCustomer" checked>
              <label class="form-check-label" for="notifyCustomer">
                Notify customer via their preferred communication method
              </label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="updateStatusBtn">
          <i class="fas fa-save me-1"></i>Update Status
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Shipping Label Modal -->
<div class="modal fade" id="shippingModal" tabindex="-1" aria-labelledby="shippingModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="shippingModalLabel">
          <i class="fas fa-shipping-fast me-2"></i>Create Shipping Label
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="shippingForm">
          <div class="mb-3">
            <label for="shippingOrderId" class="form-label">Order ID</label>
            <input type="text" class="form-control" id="shippingOrderId" readonly>
          </div>
          <div class="mb-3">
            <label for="shippingCarrier" class="form-label">Carrier</label>
            <select class="form-select" id="shippingCarrier">
              <option value="fedex">FedEx</option>
              <option value="ups">UPS</option>
              <option value="usps">USPS</option>
              <option value="dhl">DHL</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="trackingNumber" class="form-label">Tracking Number</label>
            <input type="text" class="form-control" id="trackingNumber" placeholder="Enter tracking number">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="createShippingLabel">
          <i class="fas fa-save me-1"></i>Save Shipping Info
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Tracking Modal -->
<div class="modal fade" id="trackingModal" tabindex="-1" aria-labelledby="trackingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="trackingModalLabel">
          <i class="fas fa-map-marker-alt me-2"></i>Order Tracking
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-6">
            <div class="card border-0 shadow-sm mb-3">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-shipping-fast me-2 text-info"></i>Shipping Information
                </h6>
                <div class="tracking-info">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Tracking Number:</span>
                    <div>
                      <span class="fw-bold me-2" id="modalTrackingNumber">-</span>
                      <button class="btn btn-sm btn-outline-secondary" id="copyTrackingNumber">
                        <i class="fas fa-copy"></i>
                      </button>
                    </div>
                  </div>
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Carrier:</span>
                    <span class="fw-bold" id="modalCarrier">-</span>
                  </div>
                  <div class="d-flex justify-content-between align-items-center">
                    <span>Service Type:</span>
                    <span class="fw-bold" id="modalServiceType">-</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card border-0 shadow-sm mb-3">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-clock me-2 text-warning"></i>Tracking Status
                </h6>
                <div id="trackingStatus">
                  <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading tracking information...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <h6 class="card-title">
              <i class="fas fa-route me-2 text-success"></i>Tracking Timeline
            </h6>
            <div id="trackingTimeline">
              <!-- Timeline will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-primary" id="refreshTracking">
          <i class="fas fa-sync-alt me-1"></i>Refresh
        </button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Order Items Modal -->
<div class="modal fade" id="orderItemsModal" tabindex="-1" aria-labelledby="orderItemsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="orderItemsModalLabel">
          <i class="fas fa-shopping-cart me-2"></i>Order Items - <span id="orderItemsOrderId">#</span>
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row mb-3">
          <div class="col-md-6">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-user me-2 text-primary"></i>Customer Information
                </h6>
                <div class="customer-summary">
                  <div class="mb-2">
                    <strong>Name:</strong>
                    <span id="orderItemsCustomerName">-</span>
                  </div>
                  <div class="mb-2">
                    <strong>Email:</strong>
                    <span id="orderItemsCustomerEmail">-</span>
                  </div>
                  <div class="mb-2">
                    <strong>Order Date:</strong>
                    <span id="orderItemsDate">-</span>
                  </div>
                  <div>
                    <strong>Status:</strong>
                    <span id="orderItemsStatus" class="badge">-</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-calculator me-2 text-success"></i>Order Summary
                </h6>
                <div class="order-summary">
                  <div class="d-flex justify-content-between mb-2">
                    <span>Total Items:</span>
                    <span class="fw-bold" id="orderItemsTotalItems">-</span>
                  </div>
                  <div class="d-flex justify-content-between mb-2">
                    <span>Subtotal:</span>
                    <span id="orderItemsSubtotal">-</span>
                  </div>
                  <div class="d-flex justify-content-between mb-2">
                    <span>Shipping:</span>
                    <span id="orderItemsShipping">-</span>
                  </div>
                  <hr>
                  <div class="d-flex justify-content-between">
                    <span class="fw-bold">Total:</span>
                    <span class="fw-bold text-success" id="orderItemsTotal">-</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="card border-0 shadow-sm">
          <div class="card-header bg-light">
            <h6 class="mb-0">
              <i class="fas fa-list me-2"></i>Items Ordered
            </h6>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>Product</th>
                    <th class="text-center">Quantity</th>
                    <th class="text-end">Unit Price</th>
                    <th class="text-end">Subtotal</th>
                  </tr>
                </thead>
                <tbody id="orderItemsTableBody">
                  <!-- Items will be populated by JavaScript -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-primary" id="printOrderItems">
          <i class="fas fa-print me-1"></i>Print
        </button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Customer Profile Modal -->
<div class="modal fade" id="customerProfileModal" tabindex="-1" aria-labelledby="customerProfileModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="customerProfileModalLabel">
          <i class="fas fa-user me-2"></i>Customer Profile
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-6">
            <div class="card border-0 shadow-sm mb-3">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-address-card me-2 text-primary"></i>Contact Information
                </h6>
                <div class="customer-info">
                  <div class="mb-2">
                    <strong>Name:</strong>
                    <span id="customerName">-</span>
                  </div>
                  <div class="mb-2">
                    <strong>Email:</strong>
                    <span id="customerEmail">-</span>
                  </div>
                  <div class="mb-2">
                    <strong>Phone:</strong>
                    <span id="customerPhone">-</span>
                  </div>
                  <div class="mb-2">
                    <strong>Address:</strong>
                    <div id="customerAddress" class="text-muted small">-</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card border-0 shadow-sm mb-3">
              <div class="card-body">
                <h6 class="card-title">
                  <i class="fas fa-chart-line me-2 text-success"></i>Order Statistics
                </h6>
                <div class="customer-stats">
                  <div class="d-flex justify-content-between mb-2">
                    <span>Total Orders:</span>
                    <span class="fw-bold" id="customerTotalOrders">-</span>
                  </div>
                  <div class="d-flex justify-content-between mb-2">
                    <span>Total Spent:</span>
                    <span class="fw-bold text-success" id="customerTotalSpent">-</span>
                  </div>
                  <div class="d-flex justify-content-between mb-2">
                    <span>Average Order:</span>
                    <span class="fw-bold" id="customerAvgOrder">-</span>
                  </div>
                  <div class="d-flex justify-content-between">
                    <span>Customer Since:</span>
                    <span class="fw-bold" id="customerSince">-</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <h6 class="card-title">
              <i class="fas fa-history me-2 text-info"></i>Recent Orders
            </h6>
            <div class="table-responsive">
              <table class="table table-sm">
                <thead>
                  <tr>
                    <th>Order ID</th>
                    <th>Items</th>
                    <th>Date</th>
                    <th>Amount</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody id="customerOrderHistory">
                  <!-- Order history will be populated by JavaScript -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-primary" id="viewFullCustomerProfile">
          <i class="fas fa-external-link-alt me-1"></i>View Full Profile
        </button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Styles -->
<link rel="stylesheet" href="/css/order-management.css">

<!-- Bootstrap is loaded via layout -->
<!-- Main scripts are loaded via pageScripts in controller -->

<!-- Development Testing Scripts (only in development) -->
<% if (process.env.NODE_ENV !== 'production') { %>
<script src="/js/dropdown-test.js"></script>
<script src="/js/order-management-test.js"></script>
<script src="/js/order-functionality-test.js"></script>
<script src="/js/orders-verification.js"></script>
<script src="/js/status-verification.js"></script>
<% } %>
</main>