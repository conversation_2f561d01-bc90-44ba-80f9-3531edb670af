// AI Helper JavaScript - ShopEasly Integration
// Handles API integration, chat functionality, and intelligent insights

class ShopEaslyAIHelper {
    constructor() {
        this.apiEndpoints = {
            products: 'https://4b258317-cdf4-47d9-b5b0-ea7c6e729fc0-00-1ydhxq5zezby4.picard.replit.dev/api/products',
            orders: 'https://4b258317-cdf4-47d9-b5b0-ea7c6e729fc0-00-1ydhxq5zezby4.picard.replit.dev/api/orders',
            featuredProducts: 'https://4b258317-cdf4-47d9-b5b0-ea7c6e729fc0-00-1ydhxq5zezby4.picard.replit.dev/api/products/featured'
        };
        this.cache = {
            products: null,
            orders: null,
            lastFetch: null
        };
        this.chatHistory = [];
        this.isLoading = false;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
        this.setupPODShortcuts();
        this.displayWelcomeMessage();
    }

    setupEventListeners() {
        // Chat form submission
        const chatForm = document.querySelector('.chat-input-form');
        if (chatForm) {
            chatForm.addEventListener('submit', (e) => this.handleChatSubmit(e));
        }

        // Model selector
        const modelOptions = document.querySelectorAll('.model-option');
        modelOptions.forEach(option => {
            option.addEventListener('click', () => this.switchModel(option));
        });

        // Conversation items
        const conversationItems = document.querySelectorAll('.conversation-item');
        conversationItems.forEach(item => {
            item.addEventListener('click', () => this.loadConversation(item));
        });
    }

    async loadInitialData() {
        try {
            this.showLoadingState();
            await Promise.all([
                this.fetchProducts(),
                this.fetchOrders()
            ]);
            this.hideLoadingState();
            this.updateDashboardMetrics();
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showError('Failed to load initial data. Some features may be limited.');
        }
    }

    async fetchProducts() {
        try {
            const response = await fetch(this.apiEndpoints.products);
            if (!response.ok) throw new Error(`HTTP ${response.status}`);

            const data = await response.json();
            this.cache.products = data.data || data;
            this.cache.lastFetch = Date.now();

            console.log(`✅ Loaded ${this.cache.products.length} products`);
            return this.cache.products;
        } catch (error) {
            console.error('Error fetching products:', error);
            throw error;
        }
    }

    async fetchOrders() {
        try {
            // Try the external API first, fallback to internal API
            let response;
            try {
                response = await fetch(this.apiEndpoints.orders);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);

                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    const data = await response.json();
                    this.cache.orders = data.data || data;
                } else {
                    throw new Error('Non-JSON response from external API');
                }
            } catch (externalError) {
                console.warn('External orders API failed, trying internal API:', externalError);
                // Fallback to internal API
                response = await fetch('/api/orders');
                if (!response.ok) throw new Error(`Internal API HTTP ${response.status}`);

                const data = await response.json();
                this.cache.orders = data.data || data;
            }

            this.cache.lastFetch = Date.now();
            console.log(`✅ Loaded ${this.cache.orders?.length || 0} orders`);
            return this.cache.orders;
        } catch (error) {
            console.error('Error fetching orders:', error);
            this.cache.orders = [];
            return [];
        }
    }

    updateDashboardMetrics() {
        const products = this.cache.products || [];
        const orders = this.cache.orders || [];

        // Update POD shortcuts with real data
        this.updatePODShortcut('Order Processing', `${orders.length} orders`);
        this.updatePODShortcut('Inventory & Supply Chain', `${products.length} products`);

        // Calculate featured products
        const featuredCount = products.filter(p => p.featured).length;
        this.updatePODShortcut('Marketing & Personalization', `${featuredCount} featured`);

        // Calculate revenue (if available in orders)
        const totalRevenue = orders.reduce((sum, order) => {
            return sum + (parseFloat(order.totalPrice) || 0);
        }, 0);

        if (totalRevenue > 0) {
            this.updatePODShortcut('Pricing & Profit', `$${totalRevenue.toFixed(2)}`);
        }
    }

    updatePODShortcut(name, info) {
        const shortcuts = document.querySelectorAll('.pod-shortcut-item');
        shortcuts.forEach(shortcut => {
            const span = shortcut.querySelector('span');
            if (span && span.textContent.includes(name)) {
                // Add a small info badge
                let badge = shortcut.querySelector('.info-badge');
                if (!badge) {
                    badge = document.createElement('div');
                    badge.className = 'info-badge';
                    badge.style.cssText = `
                        font-size: 0.75rem;
                        background: rgba(99, 102, 241, 0.1);
                        color: #6366f1;
                        padding: 2px 6px;
                        border-radius: 10px;
                        margin-left: auto;
                    `;
                    shortcut.appendChild(badge);
                }
                badge.textContent = info;
            }
        });
    }

    setupPODShortcuts() {
        const shortcuts = document.querySelectorAll('.pod-shortcut-item');
        shortcuts.forEach(shortcut => {
            shortcut.addEventListener('click', () => this.handlePODShortcut(shortcut));
        });
    }

    handlePODShortcut(shortcut) {
        const text = shortcut.querySelector('span').textContent;

        switch (true) {
            case text.includes('Order Processing'):
                this.handleOrderProcessingShortcut();
                break;
            case text.includes('Inventory & Supply Chain'):
                this.handleInventoryShortcut();
                break;
            case text.includes('Marketing & Personalization'):
                this.handleMarketingShortcut();
                break;
            case text.includes('Analytics & Reporting'):
                this.handleAnalyticsShortcut();
                break;
            case text.includes('Customer Service'):
                this.handleCustomerServiceShortcut();
                break;
            case text.includes('Pricing & Profit'):
                this.handlePricingShortcut();
                break;
            default:
                this.addChatMessage('ai', `Let me help you with ${text}. What specific information do you need?`);
        }
    }

    handleOrderProcessingShortcut() {
        const orders = this.cache.orders || [];
        const pendingOrders = orders.filter(o => o.status === 'pending' || o.status === 'processing');

        let message = `📦 **Order Processing Overview**\n\n`;
        message += `• Total Orders: ${orders.length}\n`;
        message += `• Pending/Processing: ${pendingOrders.length}\n`;

        if (pendingOrders.length > 0) {
            message += `\n**Recent Pending Orders:**\n`;
            pendingOrders.slice(0, 3).forEach(order => {
                message += `• Order #${order.id}: ${order.customerName} - $${order.totalPrice}\n`;
            });
        }

        message += `\nWould you like me to help with order status updates or processing workflows?`;
        this.addChatMessage('ai', message);
    }

    handleInventoryShortcut() {
        const products = this.cache.products || [];
        const lowStock = products.filter(p => p.stock < 10);
        const outOfStock = products.filter(p => p.stock === 0);

        let message = `📦 **Inventory Overview**\n\n`;
        message += `• Total Products: ${products.length}\n`;
        message += `• Low Stock (< 10): ${lowStock.length}\n`;
        message += `• Out of Stock: ${outOfStock.length}\n`;

        if (lowStock.length > 0) {
            message += `\n**Low Stock Items:**\n`;
            lowStock.slice(0, 3).forEach(product => {
                message += `• ${product.name}: ${product.stock} remaining\n`;
            });
        }

        message += `\nWould you like me to help with inventory management or restocking recommendations?`;
        this.addChatMessage('ai', message);
    }

    handleMarketingShortcut() {
        const products = this.cache.products || [];
        const featured = products.filter(p => p.featured);
        const categories = [...new Set(products.map(p => p.category))];

        let message = `🎯 **Marketing & Personalization**\n\n`;
        message += `• Featured Products: ${featured.length}\n`;
        message += `• Product Categories: ${categories.length}\n`;
        message += `• Categories: ${categories.join(', ')}\n`;

        if (featured.length > 0) {
            message += `\n**Featured Products:**\n`;
            featured.slice(0, 3).forEach(product => {
                message += `• ${product.name} - $${product.price}\n`;
            });
        }

        message += `\nWould you like help with promotional campaigns or product positioning?`;
        this.addChatMessage('ai', message);
    }

    handleAnalyticsShortcut() {
        const products = this.cache.products || [];
        const orders = this.cache.orders || [];

        const totalRevenue = orders.reduce((sum, order) => sum + (parseFloat(order.totalPrice) || 0), 0);
        const avgOrderValue = orders.length > 0 ? totalRevenue / orders.length : 0;

        let message = `📊 **Analytics & Reporting**\n\n`;
        message += `• Total Products: ${products.length}\n`;
        message += `• Total Orders: ${orders.length}\n`;
        message += `• Total Revenue: $${totalRevenue.toFixed(2)}\n`;
        message += `• Average Order Value: $${avgOrderValue.toFixed(2)}\n`;

        // Category breakdown
        const categoryStats = {};
        products.forEach(product => {
            categoryStats[product.category] = (categoryStats[product.category] || 0) + 1;
        });

        message += `\n**Product Categories:**\n`;
        Object.entries(categoryStats).forEach(([category, count]) => {
            message += `• ${category}: ${count} products\n`;
        });

        message += `\nWould you like detailed analytics or custom reports?`;
        this.addChatMessage('ai', message);
    }

    handleCustomerServiceShortcut() {
        const orders = this.cache.orders || [];
        const recentOrders = orders.slice(0, 5);

        let message = `🎧 **Customer Service Overview**\n\n`;
        message += `• Total Orders: ${orders.length}\n`;

        if (recentOrders.length > 0) {
            message += `\n**Recent Customer Orders:**\n`;
            recentOrders.forEach(order => {
                message += `• ${order.customerName}: Order #${order.id} - ${order.status}\n`;
            });
        }

        message += `\nI can help with customer inquiries, order tracking, or issue resolution. What do you need?`;
        this.addChatMessage('ai', message);
    }

    handlePricingShortcut() {
        const products = this.cache.products || [];
        const orders = this.cache.orders || [];

        const prices = products.map(p => parseFloat(p.price)).filter(p => !isNaN(p));
        const avgPrice = prices.length > 0 ? prices.reduce((a, b) => a + b, 0) / prices.length : 0;
        const minPrice = Math.min(...prices);
        const maxPrice = Math.max(...prices);

        const totalRevenue = orders.reduce((sum, order) => sum + (parseFloat(order.totalPrice) || 0), 0);

        let message = `💰 **Pricing & Profit Analysis**\n\n`;
        message += `• Average Product Price: $${avgPrice.toFixed(2)}\n`;
        message += `• Price Range: $${minPrice.toFixed(2)} - $${maxPrice.toFixed(2)}\n`;
        message += `• Total Revenue: $${totalRevenue.toFixed(2)}\n`;

        // Top selling categories by price
        const categoryRevenue = {};
        products.forEach(product => {
            const category = product.category;
            const price = parseFloat(product.price) || 0;
            categoryRevenue[category] = (categoryRevenue[category] || 0) + price;
        });

        const topCategories = Object.entries(categoryRevenue)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3);

        if (topCategories.length > 0) {
            message += `\n**Top Categories by Value:**\n`;
            topCategories.forEach(([category, value]) => {
                message += `• ${category}: $${value.toFixed(2)}\n`;
            });
        }

        message += `\nWould you like pricing optimization suggestions or profit margin analysis?`;
        this.addChatMessage('ai', message);
    }

    async handleChatSubmit(e) {
        e.preventDefault();

        const input = e.target.querySelector('input[type="text"]');
        const message = input.value.trim();

        if (!message || this.isLoading) return;

        // Add user message
        this.addChatMessage('user', message);
        input.value = '';

        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Process the message and generate AI response
            const response = await this.generateAIResponse(message);
            this.hideTypingIndicator();
            this.addChatMessage('ai', response);
        } catch (error) {
            this.hideTypingIndicator();
            this.addChatMessage('ai', 'I apologize, but I encountered an error processing your request. Please try again.');
            console.error('Chat error:', error);
        }
    }

    async generateAIResponse(userMessage) {
        const message = userMessage.toLowerCase();

        // Analyze the message for keywords and context
        if (message.includes('order') || message.includes('orders')) {
            return this.generateOrderResponse(message);
        } else if (message.includes('product') || message.includes('inventory') || message.includes('stock')) {
            return this.generateProductResponse(message);
        } else if (message.includes('customer') || message.includes('customers')) {
            return this.generateCustomerResponse(message);
        } else if (message.includes('revenue') || message.includes('sales') || message.includes('profit')) {
            return this.generateRevenueResponse(message);
        } else if (message.includes('help') || message.includes('what can you do')) {
            return this.generateHelpResponse();
        } else {
            return this.generateGeneralResponse(message);
        }
    }

    generateOrderResponse(message) {
        const orders = this.cache.orders || [];

        if (message.includes('how many') || message.includes('total')) {
            return `You currently have ${orders.length} orders in your system. ${orders.filter(o => o.status === 'pending').length} are pending processing.`;
        } else if (message.includes('recent') || message.includes('latest')) {
            const recent = orders.slice(0, 3);
            let response = `Here are your most recent orders:\n\n`;
            recent.forEach(order => {
                response += `• Order #${order.id}: ${order.customerName} - $${order.totalPrice} (${order.status})\n`;
            });
            return response;
        } else if (message.includes('pending')) {
            const pending = orders.filter(o => o.status === 'pending');
            return `You have ${pending.length} pending orders that need attention.`;
        } else {
            return `I can help you with order management. You have ${orders.length} total orders. Would you like to see recent orders, pending orders, or specific order details?`;
        }
    }

    generateProductResponse(message) {
        const products = this.cache.products || [];

        if (message.includes('how many') || message.includes('total')) {
            return `You have ${products.length} products in your inventory across ${[...new Set(products.map(p => p.category))].length} categories.`;
        } else if (message.includes('low stock') || message.includes('running low')) {
            const lowStock = products.filter(p => p.stock < 10);
            if (lowStock.length === 0) {
                return `Great news! All your products are well-stocked (10+ units).`;
            } else {
                let response = `You have ${lowStock.length} products with low stock:\n\n`;
                lowStock.slice(0, 5).forEach(product => {
                    response += `• ${product.name}: ${product.stock} remaining\n`;
                });
                return response;
            }
        } else if (message.includes('featured') || message.includes('promoted')) {
            const featured = products.filter(p => p.featured);
            return `You have ${featured.length} featured products currently promoted on your store.`;
        } else if (message.includes('categories') || message.includes('category')) {
            const categories = [...new Set(products.map(p => p.category))];
            return `Your products are organized into ${categories.length} categories: ${categories.join(', ')}.`;
        } else {
            return `I can help you with product and inventory management. You have ${products.length} products. Would you like to check stock levels, featured products, or category information?`;
        }
    }

    generateCustomerResponse(message) {
        const orders = this.cache.orders || [];
        const uniqueCustomers = [...new Set(orders.map(o => o.customerName))];

        if (message.includes('how many') || message.includes('total')) {
            return `You have served ${uniqueCustomers.length} unique customers with ${orders.length} total orders.`;
        } else {
            return `I can help you with customer information. You have ${uniqueCustomers.length} customers. Would you like to see recent customer activity or specific customer details?`;
        }
    }

    generateRevenueResponse(message) {
        const orders = this.cache.orders || [];
        const totalRevenue = orders.reduce((sum, order) => sum + (parseFloat(order.totalPrice) || 0), 0);
        const avgOrderValue = orders.length > 0 ? totalRevenue / orders.length : 0;

        let response = `📊 **Revenue Overview**\n\n`;
        response += `• Total Revenue: $${totalRevenue.toFixed(2)}\n`;
        response += `• Total Orders: ${orders.length}\n`;
        response += `• Average Order Value: $${avgOrderValue.toFixed(2)}\n`;

        if (message.includes('today') || message.includes('recent')) {
            response += `\nNote: This shows all-time totals. For daily/weekly breakdowns, I'd need access to order timestamps.`;
        }

        return response;
    }

    generateHelpResponse() {
        return `🤖 **I'm your ShopEasly AI Assistant!** Here's what I can help you with:\n\n` +
               `📦 **Orders**: Check order status, pending orders, recent activity\n` +
               `🏪 **Products**: Inventory levels, stock alerts, featured items\n` +
               `👥 **Customers**: Customer information and order history\n` +
               `💰 **Revenue**: Sales analytics and financial insights\n` +
               `📊 **Analytics**: Business metrics and performance data\n\n` +
               `Try asking me things like:\n` +
               `• "How many pending orders do I have?"\n` +
               `• "Which products are low on stock?"\n` +
               `• "What's my total revenue?"\n` +
               `• "Show me recent orders"\n\n` +
               `You can also click the POD shortcuts on the left for quick insights!`;
    }

    generateGeneralResponse(message) {
        const responses = [
            `I'm here to help you manage your ShopEasly store! What would you like to know about your orders, products, or customers?`,
            `I can provide insights about your business data. Try asking about orders, inventory, or revenue.`,
            `Let me help you with your store management. What specific information are you looking for?`,
            `I have access to your store data and can help with analysis. What would you like to explore?`
        ];

        return responses[Math.floor(Math.random() * responses.length)];
    }

    addChatMessage(sender, message) {
        const chatHistory = document.querySelector('.chat-history');
        if (!chatHistory) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${sender}`;

        const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                <div class="message-text">${this.formatMessage(message)}</div>
                <div class="message-time">${timestamp}</div>
            </div>
        `;

        chatHistory.appendChild(messageDiv);
        chatHistory.scrollTop = chatHistory.scrollHeight;

        // Store in chat history
        this.chatHistory.push({ sender, message, timestamp });
    }

    formatMessage(message) {
        // Convert markdown-like formatting to HTML
        return message
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>');
    }

    showTypingIndicator() {
        const chatHistory = document.querySelector('.chat-history');
        if (!chatHistory) return;

        const typingDiv = document.createElement('div');
        typingDiv.className = 'chat-message ai typing-indicator';
        typingDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;

        chatHistory.appendChild(typingDiv);
        chatHistory.scrollTop = chatHistory.scrollHeight;
    }

    hideTypingIndicator() {
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    displayWelcomeMessage() {
        setTimeout(() => {
            const welcomeMessage = `👋 **Welcome to your ShopEasly Command Center!**\n\n` +
                                 `I'm your AI assistant, ready to help you manage your store efficiently. ` +
                                 `I have access to your real-time product and order data.\n\n` +
                                 `**Quick Start:**\n` +
                                 `• Click any POD shortcut on the left for instant insights\n` +
                                 `• Ask me questions about your orders, inventory, or customers\n` +
                                 `• Get real-time analytics and business intelligence\n\n` +
                                 `What would you like to explore first?`;

            this.addChatMessage('ai', welcomeMessage);
        }, 1000);
    }

    switchModel(selectedOption) {
        document.querySelectorAll('.model-option').forEach(option => {
            option.classList.remove('active');
        });
        selectedOption.classList.add('active');

        const modelName = selectedOption.textContent;
        this.addChatMessage('ai', `Switched to ${modelName} model. How can I assist you?`);
    }

    loadConversation(conversationItem) {
        document.querySelectorAll('.conversation-item').forEach(item => {
            item.classList.remove('active');
        });
        conversationItem.classList.add('active');

        const conversationName = conversationItem.querySelector('.conversation-name').textContent;
        this.addChatMessage('ai', `Loaded conversation: ${conversationName}. How can I help you continue?`);
    }

    showLoadingState() {
        this.isLoading = true;
        const shortcuts = document.querySelectorAll('.pod-shortcut-item');
        shortcuts.forEach(shortcut => {
            shortcut.style.opacity = '0.6';
            shortcut.style.pointerEvents = 'none';
        });
    }

    hideLoadingState() {
        this.isLoading = false;
        const shortcuts = document.querySelectorAll('.pod-shortcut-item');
        shortcuts.forEach(shortcut => {
            shortcut.style.opacity = '1';
            shortcut.style.pointerEvents = 'auto';
        });
    }

    showError(message) {
        this.addChatMessage('ai', `⚠️ **Error**: ${message}`);
    }

    // Utility method to refresh data
    async refreshData() {
        this.addChatMessage('ai', '🔄 Refreshing data...');
        try {
            await this.loadInitialData();
            this.addChatMessage('ai', '✅ Data refreshed successfully!');
        } catch (error) {
            this.showError('Failed to refresh data. Please try again.');
        }
    }

    // Method to get specific product information
    getProductInfo(productId) {
        const products = this.cache.products || [];
        return products.find(p => p.id == productId);
    }

    // Method to get specific order information
    getOrderInfo(orderId) {
        const orders = this.cache.orders || [];
        return orders.find(o => o.id == orderId);
    }
}

// Initialize the AI Helper when the page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🤖 Initializing ShopEasly AI Helper...');
    window.aiHelper = new ShopEaslyAIHelper();
});

// Add some custom CSS for the typing indicator and message formatting
const style = document.createElement('style');
style.textContent = `
    .chat-message {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
        align-items: flex-start;
    }

    .chat-message.user {
        flex-direction: row-reverse;
    }

    .message-avatar {
        background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
        color: white;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        flex-shrink: 0;
    }

    .chat-message.user .message-avatar {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    .message-content {
        background: #f8fafc;
        border-radius: 16px;
        padding: 12px 16px;
        max-width: 70%;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .chat-message.user .message-content {
        background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
        color: white;
    }

    .message-text {
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 4px;
    }

    .message-time {
        font-size: 11px;
        opacity: 0.7;
        text-align: right;
    }

    .typing-dots {
        display: flex;
        gap: 4px;
        padding: 8px 0;
    }

    .typing-dots span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #6366f1;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
    .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

    @keyframes typing {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
    }

    .info-badge {
        font-size: 0.75rem !important;
        background: rgba(99, 102, 241, 0.1) !important;
        color: #6366f1 !important;
        padding: 2px 6px !important;
        border-radius: 10px !important;
        margin-left: auto !important;
    }
`;
document.head.appendChild(style);
