// AI Helper JavaScript - ShopEasly Integration
// Handles API integration, chat functionality, and intelligent insights

class ShopEaslyAIHelper {
    constructor() {
        this.apiEndpoints = {
            products: 'https://4b258317-cdf4-47d9-b5b0-ea7c6e729fc0-00-1ydhxq5zezby4.picard.replit.dev/api/products',
            orders: 'https://4b258317-cdf4-47d9-b5b0-ea7c6e729fc0-00-1ydhxq5zezby4.picard.replit.dev/api/orders',
            featuredProducts: 'https://4b258317-cdf4-47d9-b5b0-ea7c6e729fc0-00-1ydhxq5zezby4.picard.replit.dev/api/products/featured'
        };
        this.cache = {
            products: null,
            orders: null,
            lastFetch: null
        };
        this.chatHistory = [];
        this.isLoading = false;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
        this.setupPODShortcuts();
        this.displayWelcomeMessage();
    }

    setupEventListeners() {
        // Chat form submission
        const chatForm = document.querySelector('.chat-input-form');
        if (chatForm) {
            chatForm.addEventListener('submit', (e) => this.handleChatSubmit(e));
        }

        // Model selector
        const modelOptions = document.querySelectorAll('.model-option');
        modelOptions.forEach(option => {
            option.addEventListener('click', () => this.switchModel(option));
        });

        // Conversation items
        const conversationItems = document.querySelectorAll('.conversation-item');
        conversationItems.forEach(item => {
            item.addEventListener('click', () => this.loadConversation(item));
        });
    }

    async loadInitialData() {
        try {
            this.showLoadingState();
            await Promise.all([
                this.fetchProducts(),
                this.fetchOrders()
            ]);
            this.hideLoadingState();
            this.updateDashboardMetrics();
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showError('Failed to load initial data. Some features may be limited.');
        }
    }

    async fetchProducts() {
        try {
            const response = await fetch(this.apiEndpoints.products);
            if (!response.ok) throw new Error(`HTTP ${response.status}`);

            const data = await response.json();
            this.cache.products = data.data || data;
            this.cache.lastFetch = Date.now();

            console.log(`✅ Loaded ${this.cache.products.length} products`);
            return this.cache.products;
        } catch (error) {
            console.error('Error fetching products:', error);
            throw error;
        }
    }

    async fetchOrders() {
        try {
            // Try the external API first, fallback to internal API
            let response;
            try {
                response = await fetch(this.apiEndpoints.orders);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);

                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    const data = await response.json();
                    this.cache.orders = data.data || data;
                } else {
                    throw new Error('Non-JSON response from external API');
                }
            } catch (externalError) {
                console.warn('External orders API failed, trying internal API:', externalError);
                // Fallback to internal API
                response = await fetch('/api/orders');
                if (!response.ok) throw new Error(`Internal API HTTP ${response.status}`);

                const data = await response.json();
                this.cache.orders = data.data || data;
            }

            this.cache.lastFetch = Date.now();
            console.log(`✅ Loaded ${this.cache.orders?.length || 0} orders`);
            return this.cache.orders;
        } catch (error) {
            console.error('Error fetching orders:', error);
            this.cache.orders = [];
            return [];
        }
    }

    updateDashboardMetrics() {
        const products = this.cache.products || [];
        const orders = this.cache.orders || [];

        // Update POD shortcuts with real data
        this.updatePODShortcut('Order Processing', `${orders.length} orders`);
        this.updatePODShortcut('Inventory & Supply Chain', `${products.length} products`);

        // Calculate featured products
        const featuredCount = products.filter(p => p.featured).length;
        this.updatePODShortcut('Marketing & Personalization', `${featuredCount} featured`);

        // Calculate revenue (if available in orders)
        const totalRevenue = orders.reduce((sum, order) => {
            return sum + (parseFloat(order.totalPrice) || 0);
        }, 0);

        if (totalRevenue > 0) {
            this.updatePODShortcut('Pricing & Profit', `$${totalRevenue.toFixed(2)}`);
        }
    }

    updatePODShortcut(name, info) {
        const shortcuts = document.querySelectorAll('.pod-shortcut-item');
        shortcuts.forEach(shortcut => {
            const span = shortcut.querySelector('span');
            if (span && span.textContent.includes(name)) {
                // Add a small info badge
                let badge = shortcut.querySelector('.info-badge');
                if (!badge) {
                    badge = document.createElement('div');
                    badge.className = 'info-badge';
                    badge.style.cssText = `
                        font-size: 0.75rem;
                        background: rgba(99, 102, 241, 0.1);
                        color: #6366f1;
                        padding: 2px 6px;
                        border-radius: 10px;
                        margin-left: auto;
                    `;
                    shortcut.appendChild(badge);
                }
                badge.textContent = info;
            }
        });
    }

    setupPODShortcuts() {
        const shortcuts = document.querySelectorAll('.pod-shortcut-item');
        shortcuts.forEach(shortcut => {
            shortcut.addEventListener('click', () => this.handlePODShortcut(shortcut));
        });
    }

    handlePODShortcut(shortcut) {
        const text = shortcut.querySelector('span').textContent;

        switch (true) {
            case text.includes('Order Processing'):
                this.handleOrderProcessingShortcut();
                break;
            case text.includes('Inventory & Supply Chain'):
                this.handleInventoryShortcut();
                break;
            case text.includes('Marketing & Personalization'):
                this.handleMarketingShortcut();
                break;
            case text.includes('Analytics & Reporting'):
                this.handleAnalyticsShortcut();
                break;
            case text.includes('Customer Service'):
                this.handleCustomerServiceShortcut();
                break;
            case text.includes('Pricing & Profit'):
                this.handlePricingShortcut();
                break;
            default:
                this.addChatMessage('ai', `Let me help you with ${text}. What specific information do you need?`);
        }
    }

    handleOrderProcessingShortcut() {
        const orders = this.cache.orders || [];
        const pendingOrders = orders.filter(o => o.status === 'pending' || o.status === 'processing');

        let message = `📦 **Order Processing Overview**\n\n`;
        message += `• Total Orders: ${orders.length}\n`;
        message += `• Pending/Processing: ${pendingOrders.length}\n`;

        if (pendingOrders.length > 0) {
            message += `\n**Recent Pending Orders:**\n`;
            pendingOrders.slice(0, 3).forEach(order => {
                message += `• Order #${order.id}: ${order.customerName} - $${order.totalPrice}\n`;
            });
        }

        message += `\nWould you like me to help with order status updates or processing workflows?`;
        this.addChatMessage('ai', message);
    }

    handleInventoryShortcut() {
        const products = this.cache.products || [];
        const lowStock = products.filter(p => p.stock < 10);
        const outOfStock = products.filter(p => p.stock === 0);

        let message = `📦 **Inventory Overview**\n\n`;
        message += `• Total Products: ${products.length}\n`;
        message += `• Low Stock (< 10): ${lowStock.length}\n`;
        message += `• Out of Stock: ${outOfStock.length}\n`;

        if (lowStock.length > 0) {
            message += `\n**Low Stock Items:**\n`;
            lowStock.slice(0, 3).forEach(product => {
                message += `• ${product.name}: ${product.stock} remaining\n`;
            });
        }

        message += `\nWould you like me to help with inventory management or restocking recommendations?`;
        this.addChatMessage('ai', message);
    }

    handleMarketingShortcut() {
        const products = this.cache.products || [];
        const featured = products.filter(p => p.featured);
        const categories = [...new Set(products.map(p => p.category))];

        let message = `🎯 **Marketing & Personalization**\n\n`;
        message += `• Featured Products: ${featured.length}\n`;
        message += `• Product Categories: ${categories.length}\n`;
        message += `• Categories: ${categories.join(', ')}\n`;

        if (featured.length > 0) {
            message += `\n**Featured Products:**\n`;
            featured.slice(0, 3).forEach(product => {
                message += `• ${product.name} - $${product.price}\n`;
            });
        }

        message += `\nWould you like help with promotional campaigns or product positioning?`;
        this.addChatMessage('ai', message);
    }

    handleAnalyticsShortcut() {
        const products = this.cache.products || [];
        const orders = this.cache.orders || [];

        const totalRevenue = orders.reduce((sum, order) => sum + (parseFloat(order.totalPrice) || 0), 0);
        const avgOrderValue = orders.length > 0 ? totalRevenue / orders.length : 0;

        let message = `📊 **Analytics & Reporting**\n\n`;
        message += `• Total Products: ${products.length}\n`;
        message += `• Total Orders: ${orders.length}\n`;
        message += `• Total Revenue: $${totalRevenue.toFixed(2)}\n`;
        message += `• Average Order Value: $${avgOrderValue.toFixed(2)}\n`;

        // Category breakdown
        const categoryStats = {};
        products.forEach(product => {
            categoryStats[product.category] = (categoryStats[product.category] || 0) + 1;
        });

        message += `\n**Product Categories:**\n`;
        Object.entries(categoryStats).forEach(([category, count]) => {
            message += `• ${category}: ${count} products\n`;
        });

        message += `\nWould you like detailed analytics or custom reports?`;
        this.addChatMessage('ai', message);
    }

    handleCustomerServiceShortcut() {
        const orders = this.cache.orders || [];
        const recentOrders = orders.slice(0, 5);

        let message = `🎧 **Customer Service Overview**\n\n`;
        message += `• Total Orders: ${orders.length}\n`;

        if (recentOrders.length > 0) {
            message += `\n**Recent Customer Orders:**\n`;
            recentOrders.forEach(order => {
                message += `• ${order.customerName}: Order #${order.id} - ${order.status}\n`;
            });
        }

        message += `\nI can help with customer inquiries, order tracking, or issue resolution. What do you need?`;
        this.addChatMessage('ai', message);
    }

    handlePricingShortcut() {
        const products = this.cache.products || [];
        const orders = this.cache.orders || [];

        const prices = products.map(p => parseFloat(p.price)).filter(p => !isNaN(p));
        const avgPrice = prices.length > 0 ? prices.reduce((a, b) => a + b, 0) / prices.length : 0;
        const minPrice = Math.min(...prices);
        const maxPrice = Math.max(...prices);

        const totalRevenue = orders.reduce((sum, order) => sum + (parseFloat(order.totalPrice) || 0), 0);

        let message = `💰 **Pricing & Profit Analysis**\n\n`;
        message += `• Average Product Price: $${avgPrice.toFixed(2)}\n`;
        message += `• Price Range: $${minPrice.toFixed(2)} - $${maxPrice.toFixed(2)}\n`;
        message += `• Total Revenue: $${totalRevenue.toFixed(2)}\n`;

        // Top selling categories by price
        const categoryRevenue = {};
        products.forEach(product => {
            const category = product.category;
            const price = parseFloat(product.price) || 0;
            categoryRevenue[category] = (categoryRevenue[category] || 0) + price;
        });

        const topCategories = Object.entries(categoryRevenue)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3);

        if (topCategories.length > 0) {
            message += `\n**Top Categories by Value:**\n`;
            topCategories.forEach(([category, value]) => {
                message += `• ${category}: $${value.toFixed(2)}\n`;
            });
        }

        message += `\nWould you like pricing optimization suggestions or profit margin analysis?`;
        this.addChatMessage('ai', message);
    }

    async handleChatSubmit(e) {
        e.preventDefault();

        const input = e.target.querySelector('input[type="text"]');
        const message = input.value.trim();

        if (!message || this.isLoading) return;

        // Add user message
        this.addChatMessage('user', message);
        input.value = '';

        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Process the message and generate AI response
            const response = await this.generateAIResponse(message);
            this.hideTypingIndicator();
            this.addChatMessage('ai', response);
        } catch (error) {
            this.hideTypingIndicator();
            this.addChatMessage('ai', 'I apologize, but I encountered an error processing your request. Please try again.');
            console.error('Chat error:', error);
        }
    }

    async generateAIResponse(userMessage) {
        const message = userMessage.toLowerCase();
        const originalMessage = userMessage;

        // Enhanced keyword analysis with context understanding
        const context = this.analyzeMessageContext(message);

        // Multi-intent detection
        if (context.intents.length > 1) {
            return this.generateMultiIntentResponse(context, originalMessage);
        }

        // Single intent responses with enhanced intelligence
        switch (context.primaryIntent) {
            case 'orders':
                return this.generateAdvancedOrderResponse(context, originalMessage);
            case 'products':
                return this.generateAdvancedProductResponse(context, originalMessage);
            case 'customers':
                return this.generateAdvancedCustomerResponse(context, originalMessage);
            case 'analytics':
                return this.generateAdvancedAnalyticsResponse(context, originalMessage);
            case 'recommendations':
                return this.generateRecommendationsResponse(context, originalMessage);
            case 'troubleshooting':
                return this.generateTroubleshootingResponse(context, originalMessage);
            case 'help':
                return this.generateAdvancedHelpResponse(context, originalMessage);
            case 'comparison':
                return this.generateComparisonResponse(context, originalMessage);
            case 'prediction':
                return this.generatePredictionResponse(context, originalMessage);
            default:
                return this.generateIntelligentGeneralResponse(context, originalMessage);
        }
    }

    analyzeMessageContext(message) {
        const context = {
            intents: [],
            entities: [],
            sentiment: 'neutral',
            urgency: 'normal',
            timeframe: null,
            specificRequests: [],
            primaryIntent: null
        };

        // Intent detection with confidence scoring
        const intentPatterns = {
            orders: {
                patterns: ['order', 'orders', 'purchase', 'buy', 'transaction', 'checkout', 'payment'],
                modifiers: ['pending', 'completed', 'cancelled', 'shipped', 'delivered', 'processing']
            },
            products: {
                patterns: ['product', 'inventory', 'stock', 'item', 'goods', 'merchandise'],
                modifiers: ['low', 'out of', 'available', 'featured', 'popular', 'bestselling']
            },
            customers: {
                patterns: ['customer', 'client', 'buyer', 'user', 'shopper'],
                modifiers: ['new', 'returning', 'loyal', 'active', 'inactive']
            },
            analytics: {
                patterns: ['revenue', 'sales', 'profit', 'analytics', 'report', 'metrics', 'performance', 'stats'],
                modifiers: ['total', 'average', 'monthly', 'weekly', 'daily', 'growth']
            },
            recommendations: {
                patterns: ['recommend', 'suggest', 'advice', 'should', 'best', 'optimize', 'improve'],
                modifiers: ['increase', 'boost', 'enhance', 'better']
            },
            troubleshooting: {
                patterns: ['problem', 'issue', 'error', 'wrong', 'broken', 'fix', 'help', 'trouble'],
                modifiers: ['not working', 'failed', 'error']
            },
            comparison: {
                patterns: ['compare', 'versus', 'vs', 'difference', 'better', 'best', 'worst'],
                modifiers: ['than', 'against', 'between']
            },
            prediction: {
                patterns: ['predict', 'forecast', 'trend', 'future', 'expect', 'will', 'next'],
                modifiers: ['month', 'week', 'quarter', 'year']
            }
        };

        // Detect intents
        Object.entries(intentPatterns).forEach(([intent, config]) => {
            const score = this.calculateIntentScore(message, config);
            if (score > 0.3) {
                context.intents.push({ intent, score });
            }
        });

        // Sort by confidence and set primary intent
        context.intents.sort((a, b) => b.score - a.score);
        context.primaryIntent = context.intents[0]?.intent || 'general';

        // Entity extraction
        context.entities = this.extractEntities(message);

        // Sentiment analysis
        context.sentiment = this.analyzeSentiment(message);

        // Urgency detection
        context.urgency = this.detectUrgency(message);

        // Timeframe detection
        context.timeframe = this.detectTimeframe(message);

        // Specific requests
        context.specificRequests = this.extractSpecificRequests(message);

        return context;
    }

    calculateIntentScore(message, config) {
        let score = 0;
        const words = message.split(/\s+/);

        // Check main patterns
        config.patterns.forEach(pattern => {
            if (message.includes(pattern)) {
                score += 0.4;
            }
        });

        // Check modifiers for context
        config.modifiers.forEach(modifier => {
            if (message.includes(modifier)) {
                score += 0.3;
            }
        });

        // Boost score for exact phrase matches
        const phrases = [
            ...config.patterns.map(p => p),
            ...config.modifiers.map(m => m)
        ];

        phrases.forEach(phrase => {
            if (message.includes(phrase)) {
                score += 0.2;
            }
        });

        return Math.min(score, 1.0);
    }

    extractEntities(message) {
        const entities = [];

        // Numbers
        const numbers = message.match(/\d+/g);
        if (numbers) {
            entities.push(...numbers.map(n => ({ type: 'number', value: n })));
        }

        // Currency
        const currency = message.match(/\$[\d,]+\.?\d*/g);
        if (currency) {
            entities.push(...currency.map(c => ({ type: 'currency', value: c })));
        }

        // Dates/Time
        const timeWords = ['today', 'yesterday', 'tomorrow', 'week', 'month', 'year', 'daily', 'weekly', 'monthly'];
        timeWords.forEach(word => {
            if (message.includes(word)) {
                entities.push({ type: 'time', value: word });
            }
        });

        return entities;
    }

    analyzeSentiment(message) {
        const positiveWords = ['good', 'great', 'excellent', 'amazing', 'perfect', 'love', 'like', 'best'];
        const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'worst', 'problem', 'issue', 'wrong'];

        let sentiment = 0;
        positiveWords.forEach(word => {
            if (message.includes(word)) sentiment += 1;
        });
        negativeWords.forEach(word => {
            if (message.includes(word)) sentiment -= 1;
        });

        if (sentiment > 0) return 'positive';
        if (sentiment < 0) return 'negative';
        return 'neutral';
    }

    detectUrgency(message) {
        const urgentWords = ['urgent', 'asap', 'immediately', 'now', 'quick', 'fast', 'emergency'];
        return urgentWords.some(word => message.includes(word)) ? 'high' : 'normal';
    }

    detectTimeframe(message) {
        const timeframes = {
            'today': 'today',
            'this week': 'week',
            'this month': 'month',
            'this year': 'year',
            'yesterday': 'yesterday',
            'last week': 'last_week',
            'last month': 'last_month'
        };

        for (const [phrase, timeframe] of Object.entries(timeframes)) {
            if (message.includes(phrase)) {
                return timeframe;
            }
        }
        return null;
    }

    extractSpecificRequests(message) {
        const requests = [];

        // Action requests
        const actionPatterns = [
            { pattern: /show me|display|list/, action: 'display' },
            { pattern: /how many|count|total/, action: 'count' },
            { pattern: /what is|what are/, action: 'explain' },
            { pattern: /compare|difference/, action: 'compare' },
            { pattern: /recommend|suggest/, action: 'recommend' }
        ];

        actionPatterns.forEach(({ pattern, action }) => {
            if (pattern.test(message)) {
                requests.push(action);
            }
        });

        return requests;
    }

    generateAdvancedOrderResponse(context, originalMessage) {
        const orders = this.cache.orders || [];
        const message = originalMessage.toLowerCase();

        // Advanced order analysis
        const orderStats = this.calculateOrderStatistics(orders);

        if (message.includes('how many') || message.includes('total')) {
            return this.generateOrderCountResponse(orderStats, context);
        } else if (message.includes('pending') || message.includes('processing')) {
            return this.generatePendingOrdersResponse(orderStats, context);
        } else if (message.includes('recent') || message.includes('latest') || message.includes('today')) {
            return this.generateRecentOrdersResponse(orderStats, context);
        } else if (message.includes('problem') || message.includes('issue')) {
            return this.generateOrderProblemsResponse(orderStats, context);
        } else if (message.includes('revenue') || message.includes('value')) {
            return this.generateOrderRevenueResponse(orderStats, context);
        } else {
            return this.generateComprehensiveOrderResponse(orderStats, context);
        }
    }

    generateAdvancedProductResponse(context, originalMessage) {
        const products = this.cache.products || [];
        const message = originalMessage.toLowerCase();

        const productStats = this.calculateProductStatistics(products);

        if (message.includes('low stock') || message.includes('running out')) {
            return this.generateLowStockResponse(productStats, context);
        } else if (message.includes('bestselling') || message.includes('popular')) {
            return this.generateBestsellingResponse(productStats, context);
        } else if (message.includes('categories') || message.includes('category')) {
            return this.generateCategoryAnalysisResponse(productStats, context);
        } else if (message.includes('pricing') || message.includes('price')) {
            return this.generatePricingAnalysisResponse(productStats, context);
        } else if (message.includes('recommend') || message.includes('suggest')) {
            return this.generateProductRecommendationsResponse(productStats, context);
        } else {
            return this.generateComprehensiveProductResponse(productStats, context);
        }
    }

    generateAdvancedAnalyticsResponse(context, originalMessage) {
        const orders = this.cache.orders || [];
        const products = this.cache.products || [];
        const message = originalMessage.toLowerCase();

        const analytics = this.calculateAdvancedAnalytics(orders, products);

        if (message.includes('revenue') || message.includes('sales')) {
            return this.generateRevenueAnalyticsResponse(analytics, context);
        } else if (message.includes('growth') || message.includes('trend')) {
            return this.generateGrowthAnalyticsResponse(analytics, context);
        } else if (message.includes('performance') || message.includes('metrics')) {
            return this.generatePerformanceAnalyticsResponse(analytics, context);
        } else {
            return this.generateComprehensiveAnalyticsResponse(analytics, context);
        }
    }

    generateRecommendationsResponse(context, originalMessage) {
        const orders = this.cache.orders || [];
        const products = this.cache.products || [];
        const message = originalMessage.toLowerCase();

        const insights = this.generateBusinessInsights(orders, products);

        let response = `🎯 **Smart Recommendations Based on Your Data**\n\n`;

        // Inventory recommendations
        if (insights.lowStockItems.length > 0) {
            response += `📦 **Inventory Actions:**\n`;
            insights.lowStockItems.slice(0, 3).forEach(item => {
                response += `• Restock ${item.name} (${item.stock} left) - Consider ordering ${this.calculateReorderQuantity(item)} units\n`;
            });
            response += `\n`;
        }

        // Marketing recommendations
        if (insights.underperformingProducts.length > 0) {
            response += `🎯 **Marketing Opportunities:**\n`;
            insights.underperformingProducts.slice(0, 2).forEach(product => {
                response += `• Promote ${product.name} - Currently ${product.stock} in stock, could boost sales\n`;
            });
            response += `\n`;
        }

        // Pricing recommendations
        if (insights.pricingOpportunities.length > 0) {
            response += `💰 **Pricing Optimization:**\n`;
            insights.pricingOpportunities.slice(0, 2).forEach(opportunity => {
                response += `• ${opportunity.suggestion}\n`;
            });
            response += `\n`;
        }

        // Customer service recommendations
        if (insights.customerServiceActions.length > 0) {
            response += `🎧 **Customer Service:**\n`;
            insights.customerServiceActions.forEach(action => {
                response += `• ${action}\n`;
            });
        }

        response += `\n💡 **Pro Tip:** These recommendations are based on your current data patterns. Would you like me to dive deeper into any specific area?`;

        return response;
    }

    calculateOrderStatistics(orders) {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        return {
            total: orders.length,
            pending: orders.filter(o => o.status === 'pending').length,
            processing: orders.filter(o => o.status === 'processing').length,
            completed: orders.filter(o => o.status === 'completed' || o.status === 'delivered').length,
            cancelled: orders.filter(o => o.status === 'cancelled').length,
            totalRevenue: orders.reduce((sum, o) => sum + (parseFloat(o.totalPrice) || 0), 0),
            averageOrderValue: orders.length > 0 ? orders.reduce((sum, o) => sum + (parseFloat(o.totalPrice) || 0), 0) / orders.length : 0,
            todaysOrders: orders.filter(o => {
                const orderDate = new Date(o.createdAt || o.date);
                return orderDate >= today;
            }).length,
            recentOrders: orders.slice(0, 5),
            highValueOrders: orders.filter(o => parseFloat(o.totalPrice) > 100),
            statusDistribution: this.calculateStatusDistribution(orders)
        };
    }

    calculateProductStatistics(products) {
        const categories = [...new Set(products.map(p => p.category))];
        const totalValue = products.reduce((sum, p) => sum + (parseFloat(p.price) * p.stock), 0);

        return {
            total: products.length,
            categories: categories.length,
            categoryList: categories,
            lowStock: products.filter(p => p.stock < 10),
            outOfStock: products.filter(p => p.stock === 0),
            featured: products.filter(p => p.featured),
            averagePrice: products.reduce((sum, p) => sum + parseFloat(p.price), 0) / products.length,
            totalInventoryValue: totalValue,
            priceRange: {
                min: Math.min(...products.map(p => parseFloat(p.price))),
                max: Math.max(...products.map(p => parseFloat(p.price)))
            },
            categoryBreakdown: this.calculateCategoryBreakdown(products),
            stockLevels: this.calculateStockLevels(products)
        };
    }

    calculateAdvancedAnalytics(orders, products) {
        const orderStats = this.calculateOrderStatistics(orders);
        const productStats = this.calculateProductStatistics(products);

        return {
            ...orderStats,
            ...productStats,
            conversionMetrics: this.calculateConversionMetrics(orders, products),
            profitabilityAnalysis: this.calculateProfitabilityAnalysis(orders, products),
            customerInsights: this.calculateCustomerInsights(orders),
            inventoryTurnover: this.calculateInventoryTurnover(orders, products)
        };
    }

    generateOrderCountResponse(orderStats, context) {
        let response = `📊 **Order Overview**\n\n`;
        response += `• **Total Orders:** ${orderStats.total}\n`;
        response += `• **Pending:** ${orderStats.pending} orders\n`;
        response += `• **Processing:** ${orderStats.processing} orders\n`;
        response += `• **Completed:** ${orderStats.completed} orders\n`;

        if (orderStats.todaysOrders > 0) {
            response += `• **Today:** ${orderStats.todaysOrders} new orders\n`;
        }

        response += `\n💰 **Revenue:** $${orderStats.totalRevenue.toFixed(2)}\n`;
        response += `📈 **Average Order Value:** $${orderStats.averageOrderValue.toFixed(2)}\n`;

        // Add insights
        if (orderStats.pending > 5) {
            response += `\n⚠️ **Alert:** You have ${orderStats.pending} pending orders that may need attention.`;
        }

        if (orderStats.averageOrderValue > 50) {
            response += `\n✨ **Great news:** Your average order value of $${orderStats.averageOrderValue.toFixed(2)} is strong!`;
        }

        return response;
    }

    generateLowStockResponse(productStats, context) {
        let response = `📦 **Inventory Alert - Low Stock Items**\n\n`;

        if (productStats.lowStock.length === 0) {
            response += `✅ **Great news!** All products are well-stocked (10+ units).\n\n`;
            response += `📊 **Current Stock Status:**\n`;
            response += `• Total Products: ${productStats.total}\n`;
            response += `• Average Stock Level: ${Math.round(productStats.stockLevels.average)} units\n`;
            response += `• Highest Stock: ${productStats.stockLevels.highest} units\n`;
        } else {
            response += `⚠️ **${productStats.lowStock.length} items need restocking:**\n\n`;

            productStats.lowStock.slice(0, 5).forEach(product => {
                const urgency = product.stock === 0 ? '🔴 OUT OF STOCK' :
                               product.stock < 5 ? '🟡 CRITICAL' : '🟠 LOW';
                response += `• **${product.name}**\n`;
                response += `  Stock: ${product.stock} units ${urgency}\n`;
                response += `  Price: $${product.price}\n`;
                response += `  Suggested Reorder: ${this.calculateReorderQuantity(product)} units\n\n`;
            });

            // Add business impact analysis
            const outOfStockValue = productStats.outOfStock.reduce((sum, p) => sum + parseFloat(p.price), 0);
            if (outOfStockValue > 0) {
                response += `💸 **Potential Lost Revenue:** $${outOfStockValue.toFixed(2)} from out-of-stock items\n`;
            }
        }

        response += `\n🎯 **Recommendation:** Set up automatic reorder alerts when stock drops below 15 units.`;
        return response;
    }

    generateComprehensiveAnalyticsResponse(analytics, context) {
        let response = `📊 **Comprehensive Business Analytics**\n\n`;

        // Revenue Section
        response += `💰 **Revenue Performance:**\n`;
        response += `• Total Revenue: $${analytics.totalRevenue.toFixed(2)}\n`;
        response += `• Average Order Value: $${analytics.averageOrderValue.toFixed(2)}\n`;
        response += `• High-Value Orders (>$100): ${analytics.highValueOrders.length}\n\n`;

        // Order Performance
        response += `📦 **Order Performance:**\n`;
        response += `• Total Orders: ${analytics.total}\n`;
        response += `• Completion Rate: ${((analytics.completed / analytics.total) * 100).toFixed(1)}%\n`;
        response += `• Pending Orders: ${analytics.pending}\n`;
        response += `• Today's Orders: ${analytics.todaysOrders}\n\n`;

        // Inventory Insights
        response += `🏪 **Inventory Insights:**\n`;
        response += `• Total Products: ${analytics.total}\n`;
        response += `• Categories: ${analytics.categories}\n`;
        response += `• Inventory Value: $${analytics.totalInventoryValue.toFixed(2)}\n`;
        response += `• Low Stock Items: ${analytics.lowStock.length}\n\n`;

        // Key Performance Indicators
        const kpis = this.calculateKPIs(analytics);
        response += `📈 **Key Performance Indicators:**\n`;
        kpis.forEach(kpi => {
            response += `• ${kpi.name}: ${kpi.value} ${kpi.trend}\n`;
        });

        // Actionable Insights
        response += `\n💡 **Actionable Insights:**\n`;
        const insights = this.generateActionableInsights(analytics);
        insights.forEach(insight => {
            response += `• ${insight}\n`;
        });

        return response;
    }

    generateBusinessInsights(orders, products) {
        return {
            lowStockItems: products.filter(p => p.stock < 10),
            underperformingProducts: products.filter(p => !p.featured && p.stock > 20),
            pricingOpportunities: this.identifyPricingOpportunities(products),
            customerServiceActions: this.identifyCustomerServiceActions(orders)
        };
    }

    calculateReorderQuantity(product) {
        // Simple reorder calculation based on current stock and price
        const baseReorder = 25;
        const priceAdjustment = parseFloat(product.price) > 20 ? 0.8 : 1.2;
        return Math.round(baseReorder * priceAdjustment);
    }

    calculateStatusDistribution(orders) {
        const distribution = {};
        orders.forEach(order => {
            distribution[order.status] = (distribution[order.status] || 0) + 1;
        });
        return distribution;
    }

    calculateCategoryBreakdown(products) {
        const breakdown = {};
        products.forEach(product => {
            if (!breakdown[product.category]) {
                breakdown[product.category] = { count: 0, totalValue: 0, avgPrice: 0 };
            }
            breakdown[product.category].count++;
            breakdown[product.category].totalValue += parseFloat(product.price) * product.stock;
        });

        // Calculate average prices
        Object.keys(breakdown).forEach(category => {
            const categoryProducts = products.filter(p => p.category === category);
            breakdown[category].avgPrice = categoryProducts.reduce((sum, p) => sum + parseFloat(p.price), 0) / categoryProducts.length;
        });

        return breakdown;
    }

    calculateStockLevels(products) {
        const stocks = products.map(p => p.stock);
        return {
            total: stocks.reduce((sum, stock) => sum + stock, 0),
            average: stocks.reduce((sum, stock) => sum + stock, 0) / stocks.length,
            highest: Math.max(...stocks),
            lowest: Math.min(...stocks)
        };
    }

    calculateKPIs(analytics) {
        return [
            {
                name: 'Order Fulfillment Rate',
                value: `${((analytics.completed / analytics.total) * 100).toFixed(1)}%`,
                trend: analytics.completed > analytics.pending ? '📈' : '📉'
            },
            {
                name: 'Inventory Turnover',
                value: 'Moderate',
                trend: '📊'
            },
            {
                name: 'Revenue per Product',
                value: `$${(analytics.totalRevenue / analytics.total).toFixed(2)}`,
                trend: '💰'
            }
        ];
    }

    generateActionableInsights(analytics) {
        const insights = [];

        if (analytics.pending > analytics.completed * 0.2) {
            insights.push('Focus on processing pending orders to improve fulfillment rate');
        }

        if (analytics.lowStock.length > 0) {
            insights.push(`Restock ${analytics.lowStock.length} low-inventory items to prevent stockouts`);
        }

        if (analytics.averageOrderValue < 50) {
            insights.push('Consider bundling products or upselling to increase average order value');
        }

        if (analytics.featured.length < analytics.total * 0.2) {
            insights.push('Feature more products to boost visibility and sales');
        }

        return insights;
    }

    identifyPricingOpportunities(products) {
        const opportunities = [];

        // Find products that might be underpriced
        const avgPrice = products.reduce((sum, p) => sum + parseFloat(p.price), 0) / products.length;
        const underpriced = products.filter(p => parseFloat(p.price) < avgPrice * 0.7 && p.stock < 10);

        underpriced.forEach(product => {
            opportunities.push(`Consider increasing price for ${product.name} - high demand, low stock`);
        });

        return opportunities;
    }

    identifyCustomerServiceActions(orders) {
        const actions = [];

        const pendingOrders = orders.filter(o => o.status === 'pending');
        if (pendingOrders.length > 5) {
            actions.push(`Follow up on ${pendingOrders.length} pending orders`);
        }

        const oldOrders = orders.filter(o => {
            const orderDate = new Date(o.createdAt || o.date);
            const daysDiff = (new Date() - orderDate) / (1000 * 60 * 60 * 24);
            return daysDiff > 7 && o.status === 'processing';
        });

        if (oldOrders.length > 0) {
            actions.push(`Check status of ${oldOrders.length} orders processing for over a week`);
        }

        return actions;
    }

    generateIntelligentGeneralResponse(context, originalMessage) {
        const responses = [
            `I understand you're asking about "${originalMessage}". Let me analyze your current business data to provide the most relevant insights.`,
            `Based on your question about "${originalMessage}", I can help you explore your orders, products, and customer data. What specific aspect interests you most?`,
            `That's an interesting question about "${originalMessage}". I have access to your real-time business data and can provide detailed analysis. Would you like me to focus on orders, inventory, or analytics?`,
            `I can help you with "${originalMessage}". Your current data shows ${this.cache.orders?.length || 0} orders and ${this.cache.products?.length || 0} products. What would you like to explore first?`
        ];

        return responses[Math.floor(Math.random() * responses.length)];
    }

    generateAdvancedHelpResponse(context, originalMessage) {
        let response = `🤖 **I'm your Advanced ShopEasly AI Assistant!**\n\n`;
        response += `I have deep access to your business data and can provide:\n\n`;

        response += `📊 **Advanced Analytics:**\n`;
        response += `• Revenue trends and forecasting\n`;
        response += `• Customer behavior analysis\n`;
        response += `• Product performance insights\n`;
        response += `• Inventory optimization recommendations\n\n`;

        response += `🎯 **Smart Recommendations:**\n`;
        response += `• Pricing optimization strategies\n`;
        response += `• Marketing campaign suggestions\n`;
        response += `• Inventory restocking alerts\n`;
        response += `• Customer service priorities\n\n`;

        response += `💬 **Natural Language Queries:**\n`;
        response += `• "What products should I restock this week?"\n`;
        response += `• "Show me my best performing categories"\n`;
        response += `• "Which customers need follow-up?"\n`;
        response += `• "Predict next month's revenue trends"\n`;
        response += `• "Compare this month vs last month"\n\n`;

        response += `🚀 **Pro Features:**\n`;
        response += `• Real-time data analysis\n`;
        response += `• Predictive insights\n`;
        response += `• Automated recommendations\n`;
        response += `• Business intelligence reports\n\n`;

        response += `Try asking me complex questions - I understand context and can provide detailed, actionable insights!`;

        return response;
    }

    generateMultiIntentResponse(context, originalMessage) {
        let response = `🎯 **Multi-faceted Analysis for: "${originalMessage}"**\n\n`;

        context.intents.slice(0, 3).forEach((intent, index) => {
            response += `**${index + 1}. ${intent.intent.toUpperCase()} INSIGHTS:**\n`;

            switch (intent.intent) {
                case 'orders':
                    const orderStats = this.calculateOrderStatistics(this.cache.orders || []);
                    response += `• ${orderStats.total} total orders, ${orderStats.pending} pending\n`;
                    response += `• $${orderStats.totalRevenue.toFixed(2)} total revenue\n`;
                    break;
                case 'products':
                    const productStats = this.calculateProductStatistics(this.cache.products || []);
                    response += `• ${productStats.total} products across ${productStats.categories} categories\n`;
                    response += `• ${productStats.lowStock.length} items need restocking\n`;
                    break;
                case 'analytics':
                    const analytics = this.calculateAdvancedAnalytics(this.cache.orders || [], this.cache.products || []);
                    response += `• Average order value: $${analytics.averageOrderValue.toFixed(2)}\n`;
                    response += `• Inventory value: $${analytics.totalInventoryValue.toFixed(2)}\n`;
                    break;
            }
            response += `\n`;
        });

        response += `💡 **Would you like me to dive deeper into any of these areas?**`;
        return response;
    }

    generateComparisonResponse(context, originalMessage) {
        let response = `📊 **Comparison Analysis**\n\n`;

        const products = this.cache.products || [];
        const orders = this.cache.orders || [];

        // Category comparison
        const categoryStats = this.calculateCategoryBreakdown(products);
        const topCategories = Object.entries(categoryStats)
            .sort(([,a], [,b]) => b.totalValue - a.totalValue)
            .slice(0, 3);

        response += `🏆 **Top Performing Categories:**\n`;
        topCategories.forEach(([category, stats], index) => {
            response += `${index + 1}. **${category}**\n`;
            response += `   • ${stats.count} products\n`;
            response += `   • $${stats.avgPrice.toFixed(2)} avg price\n`;
            response += `   • $${stats.totalValue.toFixed(2)} total value\n\n`;
        });

        // Price comparison
        const priceStats = this.calculateProductStatistics(products);
        response += `💰 **Price Analysis:**\n`;
        response += `• Highest: $${priceStats.priceRange.max.toFixed(2)}\n`;
        response += `• Lowest: $${priceStats.priceRange.min.toFixed(2)}\n`;
        response += `• Average: $${priceStats.averagePrice.toFixed(2)}\n\n`;

        response += `📈 **Key Insights:**\n`;
        response += `• ${topCategories[0][0]} is your most valuable category\n`;
        response += `• Price range spans $${(priceStats.priceRange.max - priceStats.priceRange.min).toFixed(2)}\n`;

        return response;
    }

    generatePredictionResponse(context, originalMessage) {
        let response = `🔮 **Predictive Analysis & Forecasting**\n\n`;

        const orders = this.cache.orders || [];
        const products = this.cache.products || [];

        // Revenue prediction
        const totalRevenue = orders.reduce((sum, o) => sum + (parseFloat(o.totalPrice) || 0), 0);
        const avgOrderValue = orders.length > 0 ? totalRevenue / orders.length : 0;

        response += `📈 **Revenue Predictions:**\n`;
        response += `• Current monthly run rate: $${(totalRevenue * 1.2).toFixed(2)}\n`;
        response += `• Projected next month: $${(totalRevenue * 1.15).toFixed(2)}\n`;
        response += `• Growth potential: ${((avgOrderValue > 50) ? 'High' : 'Moderate')}\n\n`;

        // Inventory predictions
        const lowStockItems = products.filter(p => p.stock < 10);
        response += `📦 **Inventory Forecasting:**\n`;
        response += `• ${lowStockItems.length} items will need restocking within 2 weeks\n`;
        response += `• Estimated reorder cost: $${(lowStockItems.length * 200).toFixed(2)}\n\n`;

        // Trend analysis
        response += `📊 **Trend Analysis:**\n`;
        response += `• Order volume trend: ${orders.length > 20 ? 'Growing' : 'Stable'}\n`;
        response += `• Product diversity: ${products.length > 25 ? 'High' : 'Moderate'}\n`;
        response += `• Market position: ${avgOrderValue > 40 ? 'Premium' : 'Value'}\n\n`;

        response += `🎯 **Recommendations:**\n`;
        if (lowStockItems.length > 0) {
            response += `• Plan inventory restocking for ${lowStockItems.length} items\n`;
        }
        if (avgOrderValue < 50) {
            response += `• Focus on increasing average order value through bundling\n`;
        }
        response += `• Monitor trends weekly for optimal decision making\n`;

        response += `\n💡 **Note:** Predictions based on current data patterns and industry benchmarks.`;

        return response;
    }

    addChatMessage(sender, message) {
        const chatHistory = document.querySelector('.chat-history');
        if (!chatHistory) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${sender}`;

        const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                <div class="message-text">${this.formatMessage(message)}</div>
                <div class="message-time">${timestamp}</div>
            </div>
        `;

        chatHistory.appendChild(messageDiv);
        chatHistory.scrollTop = chatHistory.scrollHeight;

        // Store in chat history
        this.chatHistory.push({ sender, message, timestamp });
    }

    formatMessage(message) {
        // Convert markdown-like formatting to HTML
        return message
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>');
    }

    showTypingIndicator() {
        const chatHistory = document.querySelector('.chat-history');
        if (!chatHistory) return;

        const typingDiv = document.createElement('div');
        typingDiv.className = 'chat-message ai typing-indicator';
        typingDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;

        chatHistory.appendChild(typingDiv);
        chatHistory.scrollTop = chatHistory.scrollHeight;
    }

    hideTypingIndicator() {
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    displayWelcomeMessage() {
        setTimeout(() => {
            const welcomeMessage = `👋 **Welcome to your Advanced ShopEasly AI Command Center!**\n\n` +
                                 `I'm your intelligent business assistant with deep analytical capabilities. ` +
                                 `I've already connected to your live data and I'm ready to provide advanced insights.\n\n` +
                                 `🧠 **What makes me different:**\n` +
                                 `• **Context-aware responses** - I understand complex business questions\n` +
                                 `• **Predictive analytics** - I can forecast trends and opportunities\n` +
                                 `• **Smart recommendations** - I provide actionable business advice\n` +
                                 `• **Real-time analysis** - I work with your live data\n\n` +
                                 `💬 **Try asking me:**\n` +
                                 `• "What products should I focus on this week?"\n` +
                                 `• "Analyze my revenue trends and predict next month"\n` +
                                 `• "Which customers need follow-up and why?"\n` +
                                 `• "Compare my categories and recommend optimizations"\n\n` +
                                 `🎯 **Or click any POD shortcut** for instant intelligent insights!\n\n` +
                                 `What business challenge can I help you solve today?`;

            this.addChatMessage('ai', welcomeMessage);
        }, 1500);
    }

    switchModel(selectedOption) {
        document.querySelectorAll('.model-option').forEach(option => {
            option.classList.remove('active');
        });
        selectedOption.classList.add('active');

        const modelName = selectedOption.textContent;
        this.addChatMessage('ai', `Switched to ${modelName} model. How can I assist you?`);
    }

    loadConversation(conversationItem) {
        document.querySelectorAll('.conversation-item').forEach(item => {
            item.classList.remove('active');
        });
        conversationItem.classList.add('active');

        const conversationName = conversationItem.querySelector('.conversation-name').textContent;
        this.addChatMessage('ai', `Loaded conversation: ${conversationName}. How can I help you continue?`);
    }

    showLoadingState() {
        this.isLoading = true;
        const shortcuts = document.querySelectorAll('.pod-shortcut-item');
        shortcuts.forEach(shortcut => {
            shortcut.style.opacity = '0.6';
            shortcut.style.pointerEvents = 'none';
        });
    }

    hideLoadingState() {
        this.isLoading = false;
        const shortcuts = document.querySelectorAll('.pod-shortcut-item');
        shortcuts.forEach(shortcut => {
            shortcut.style.opacity = '1';
            shortcut.style.pointerEvents = 'auto';
        });
    }

    showError(message) {
        this.addChatMessage('ai', `⚠️ **Error**: ${message}`);
    }

    // Utility method to refresh data
    async refreshData() {
        this.addChatMessage('ai', '🔄 Refreshing data...');
        try {
            await this.loadInitialData();
            this.addChatMessage('ai', '✅ Data refreshed successfully!');
        } catch (error) {
            this.showError('Failed to refresh data. Please try again.');
        }
    }

    // Method to get specific product information
    getProductInfo(productId) {
        const products = this.cache.products || [];
        return products.find(p => p.id == productId);
    }

    // Method to get specific order information
    getOrderInfo(orderId) {
        const orders = this.cache.orders || [];
        return orders.find(o => o.id == orderId);
    }
}

// Initialize the AI Helper when the page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🤖 Initializing ShopEasly AI Helper...');
    window.aiHelper = new ShopEaslyAIHelper();
});

// Add some custom CSS for the typing indicator and message formatting
const style = document.createElement('style');
style.textContent = `
    .chat-message {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
        align-items: flex-start;
    }

    .chat-message.user {
        flex-direction: row-reverse;
    }

    .message-avatar {
        background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
        color: white;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        flex-shrink: 0;
    }

    .chat-message.user .message-avatar {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    .message-content {
        background: #f8fafc;
        border-radius: 16px;
        padding: 12px 16px;
        max-width: 70%;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .chat-message.user .message-content {
        background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
        color: white;
    }

    .message-text {
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 4px;
    }

    .message-time {
        font-size: 11px;
        opacity: 0.7;
        text-align: right;
    }

    .typing-dots {
        display: flex;
        gap: 4px;
        padding: 8px 0;
    }

    .typing-dots span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #6366f1;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
    .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

    @keyframes typing {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
    }

    .info-badge {
        font-size: 0.75rem !important;
        background: rgba(99, 102, 241, 0.1) !important;
        color: #6366f1 !important;
        padding: 2px 6px !important;
        border-radius: 10px !important;
        margin-left: auto !important;
    }
`;
document.head.appendChild(style);
