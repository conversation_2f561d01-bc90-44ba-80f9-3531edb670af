# Shopping Website Integration Guide

## 🔗 Connect Your Existing Shopping Website to ShopEasly Admin

This guide shows you how to integrate your existing shopping website with your ShopEasly Admin backend for real-time order management, inventory checking, and customer notifications.

## 📋 Prerequisites

- Your ShopEasly Admin is deployed (Render, Replit, or other platform)
- You have your API key set in environment variables
- Your shopping website can make HTTP requests

## 🌐 Platform-Specific URLs

### **Render Deployment:**
```
https://shop-easly-admin.onrender.com
```

### **Replit Deployment:**
```
https://[your-replit-username]-[your-repl-name].replit.app
```
Example: `https://johndoe-shopeasly-admin.replit.app`

### **Local Development:**
```
http://localhost:3000
```

## 🔑 API Authentication

All production API endpoints require authentication via API key:

```javascript
// Add this header to all requests
headers: {
  'X-API-Key': 'your-secure-api-key-here',
  'Content-Type': 'application/json'
}
```

## 🛒 Integration Points

### 1. **Inventory Stock Checking** (Before Checkout)

Check if products are available before allowing customers to complete their purchase:

```javascript
// Single product check
async function checkProductStock(productId, quantity = 1) {
  try {
    const response = await fetch(
      `https://[your-replit-domain].replit.app/api/production/inventory/check/${productId}?quantity=${quantity}`,
      {
        headers: {
          'X-API-Key': 'your-api-key-here'
        }
      }
    );
    
    const result = await response.json();
    
    if (result.success) {
      return {
        inStock: result.data.isInStock,
        availableStock: result.data.availableStock,
        stockStatus: result.data.stockStatus // 'in_stock', 'low_stock', 'out_of_stock'
      };
    }
    
    throw new Error(result.message);
  } catch (error) {
    console.error('Stock check failed:', error);
    return { inStock: false, error: error.message };
  }
}

// Usage in your shopping cart
const stockCheck = await checkProductStock('TSH_COTTON_BLK_M', 2);
if (!stockCheck.inStock) {
  alert('Sorry, this item is out of stock');
  return;
}
```

### 2. **Bulk Inventory Check** (Shopping Cart Validation)

Check multiple products at once before checkout:

```javascript
async function validateShoppingCart(cartItems) {
  try {
    const products = cartItems.map(item => ({
      productId: item.productId,
      quantity: item.quantity
    }));
    
    const response = await fetch(
      'https://shop-easly-admin.onrender.com/api/production/inventory/bulk-check',
      {
        method: 'POST',
        headers: {
          'X-API-Key': 'your-api-key-here',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ products })
      }
    );
    
    const result = await response.json();
    
    if (result.success) {
      return {
        canFulfillOrder: result.data.canFulfillEntireOrder,
        productResults: result.data.products
      };
    }
    
    throw new Error(result.message);
  } catch (error) {
    console.error('Cart validation failed:', error);
    return { canFulfillOrder: false, error: error.message };
  }
}

// Usage before checkout
const cartValidation = await validateShoppingCart(shoppingCart);
if (!cartValidation.canFulfillOrder) {
  // Show out of stock items to customer
  const outOfStock = cartValidation.productResults.filter(p => !p.isInStock);
  showOutOfStockMessage(outOfStock);
  return;
}
```

### 3. **Order Submission** (After Payment Success)

Send completed orders to your admin system:

```javascript
async function submitOrderToAdmin(orderData) {
  try {
    const orderPayload = {
      orderId: orderData.orderId, // Your unique order ID
      customerName: orderData.customer.name,
      customerEmail: orderData.customer.email,
      customerPhone: orderData.customer.phone,
      customerAddress: orderData.customer.address,
      items: orderData.items.map(item => ({
        productId: item.productId,
        productName: item.name,
        quantity: item.quantity,
        price: item.price,
        sku: item.sku
      })),
      totalPrice: orderData.total,
      paymentStatus: 'completed', // or 'pending'
      shippingMethod: orderData.shipping.method,
      notes: orderData.specialInstructions || null
    };
    
    const response = await fetch(
      'https://shop-easly-admin.onrender.com/api/production/orders',
      {
        method: 'POST',
        headers: {
          'X-API-Key': 'your-api-key-here',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderPayload)
      }
    );
    
    const result = await response.json();
    
    if (result.success) {
      console.log('Order submitted to admin:', result.data.orderId);
      return result.data;
    }
    
    throw new Error(result.message);
  } catch (error) {
    console.error('Order submission failed:', error);
    // Handle error - maybe retry or log for manual processing
    throw error;
  }
}

// Usage after payment success
try {
  await submitOrderToAdmin(completedOrder);
  // Show success message to customer
  showOrderConfirmation(completedOrder.orderId);
} catch (error) {
  // Handle error gracefully
  console.error('Failed to submit order to admin system:', error);
  // Order was paid for, so log this for manual processing
  logOrderForManualProcessing(completedOrder, error);
}
```

### 4. **Order Status Tracking** (Customer Account Page)

Allow customers to check their order status:

```javascript
async function getOrderStatus(orderId) {
  try {
    const response = await fetch(
      `https://shop-easly-admin.onrender.com/api/production/orders/${orderId}/status`,
      {
        headers: {
          'X-API-Key': 'your-api-key-here'
        }
      }
    );
    
    const result = await response.json();
    
    if (result.success) {
      return {
        status: result.data.status,
        trackingNumber: result.data.trackingNumber,
        estimatedDelivery: result.data.estimatedDelivery,
        lastUpdated: result.data.lastUpdated
      };
    }
    
    throw new Error(result.message);
  } catch (error) {
    console.error('Status check failed:', error);
    return { error: error.message };
  }
}

// Usage in customer account page
const orderStatus = await getOrderStatus('ORDER_123456');
if (orderStatus.status) {
  updateOrderStatusDisplay(orderStatus);
}
```

## 🔄 Complete Integration Example

Here's a complete example showing how to integrate all the APIs into your checkout process:

```javascript
class ShopEaslyIntegration {
  constructor(apiKey, baseUrl = 'https://shop-easly-admin.onrender.com') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }
  
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseUrl}/api/production${endpoint}`;
    const config = {
      headers: {
        'X-API-Key': this.apiKey,
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };
    
    const response = await fetch(url, config);
    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message || 'API request failed');
    }
    
    return result.data;
  }
  
  // Check if products are in stock
  async validateCart(cartItems) {
    const products = cartItems.map(item => ({
      productId: item.productId,
      quantity: item.quantity
    }));
    
    return this.makeRequest('/inventory/bulk-check', {
      method: 'POST',
      body: JSON.stringify({ products })
    });
  }
  
  // Submit order after payment
  async submitOrder(orderData) {
    return this.makeRequest('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData)
    });
  }
  
  // Get order status for customer
  async getOrderStatus(orderId) {
    return this.makeRequest(`/orders/${orderId}/status`);
  }
}

// Usage in your shopping website
const shopEasly = new ShopEaslyIntegration('your-api-key-here');

// Before checkout
const cartValidation = await shopEasly.validateCart(shoppingCart);
if (!cartValidation.canFulfillEntireOrder) {
  // Handle out of stock items
  return;
}

// After payment success
const orderResult = await shopEasly.submitOrder({
  orderId: 'WEB_' + Date.now(),
  customerName: customer.name,
  customerEmail: customer.email,
  customerPhone: customer.phone,
  customerAddress: customer.address,
  items: cartItems,
  totalPrice: orderTotal,
  paymentStatus: 'completed',
  shippingMethod: selectedShipping
});

console.log('Order submitted:', orderResult.orderId);
```

## 🚨 Error Handling Best Practices

```javascript
// Robust error handling for production
async function safeApiCall(apiFunction, fallbackAction) {
  try {
    return await apiFunction();
  } catch (error) {
    console.error('API call failed:', error);
    
    // Log error for monitoring
    logError('ShopEasly API Error', error);
    
    // Execute fallback action
    if (fallbackAction) {
      return fallbackAction(error);
    }
    
    throw error;
  }
}

// Usage
const stockCheck = await safeApiCall(
  () => shopEasly.validateCart(cart),
  (error) => {
    // Fallback: Allow order but flag for manual review
    logOrderForManualReview(cart, 'Stock check failed');
    return { canFulfillEntireOrder: true, requiresManualReview: true };
  }
);
```

## 📊 Monitoring & Analytics

Track integration health:

```javascript
// Monitor API response times and success rates
class APIMonitor {
  static async trackApiCall(apiName, apiFunction) {
    const startTime = Date.now();
    
    try {
      const result = await apiFunction();
      const duration = Date.now() - startTime;
      
      // Log successful API call
      analytics.track('api_success', {
        api: apiName,
        duration: duration,
        timestamp: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Log failed API call
      analytics.track('api_error', {
        api: apiName,
        error: error.message,
        duration: duration,
        timestamp: new Date().toISOString()
      });
      
      throw error;
    }
  }
}

// Usage
const orderResult = await APIMonitor.trackApiCall(
  'submit_order',
  () => shopEasly.submitOrder(orderData)
);
```

## 🔧 Environment Configuration

Set up different environments:

```javascript
const config = {
  development: {
    apiUrl: 'http://localhost:3000',
    apiKey: 'dev-api-key'
  },
  production: {
    apiUrl: 'https://shop-easly-admin.onrender.com',
    apiKey: process.env.SHOPEASLY_API_KEY
  }
};

const env = process.env.NODE_ENV || 'development';
const shopEasly = new ShopEaslyIntegration(
  config[env].apiKey,
  config[env].apiUrl
);
```

## 🎯 Next Steps

1. **Set your API key** in environment variables
2. **Test the integration** with the provided examples
3. **Implement error handling** for production reliability
4. **Monitor API calls** for performance and errors
5. **Set up webhooks** for real-time order status updates (if needed)

Your ShopEasly Admin will automatically:
- ✅ Receive and store all orders
- ✅ Update inventory levels
- ✅ Create customer profiles
- ✅ Send status update notifications
- ✅ Provide order management interface

The integration is designed to be **reliable**, **scalable**, and **easy to maintain**!
