<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Website Integration Tester</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .integration-section {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .result-display {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #198754; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #0d6efd; }
        .warning { color: #fd7e14; }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin: 2px;
        }
        .in-stock { background: #d1edff; color: #0969da; }
        .low-stock { background: #fff3cd; color: #664d03; }
        .out-of-stock { background: #f8d7da; color: #721c24; }
        .api-config {
            background: #e7f3ff;
            border: 1px solid #b6d7ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-plug me-2 text-primary"></i>Shopping Website Integration Tester
                </h1>
                <p class="text-muted">Test the integration between your shopping website and ShopEasly Admin</p>
            </div>
        </div>

        <!-- API Configuration -->
        <div class="api-config">
            <h5><i class="fas fa-cog me-2"></i>API Configuration</h5>
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">API Key</label>
                    <input type="password" class="form-control" id="apiKey" placeholder="Enter your API key">
                </div>
                <div class="col-md-6">
                    <label class="form-label">Admin URL</label>
                    <input type="url" class="form-control" id="adminUrl" value="https://[your-username]-shopeasly-admin.replit.app" placeholder="Enter your deployment URL">
                    <div class="form-text">
                        Examples:<br>
                        • Replit: https://username-shopeasly-admin.replit.app<br>
                        • Render: https://shop-easly-admin.onrender.com<br>
                        • Local: http://localhost:3000
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <button class="btn btn-primary btn-sm" onclick="testConnection()">
                    <i class="fas fa-wifi me-1"></i>Test Connection
                </button>
                <span id="connectionStatus" class="ms-3"></span>
            </div>
        </div>

        <!-- Stock Checking -->
        <div class="integration-section">
            <h3><i class="fas fa-boxes me-2 text-success"></i>Inventory Stock Checking</h3>
            <p>Test checking product availability before customers add items to cart</p>
            
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">Product ID</label>
                    <input type="text" class="form-control" id="stockProductId" value="TSH_COTTON_BLK_M">
                </div>
                <div class="col-md-4">
                    <label class="form-label">Quantity</label>
                    <input type="number" class="form-control" id="stockQuantity" value="2" min="1">
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button class="btn btn-success" onclick="checkSingleStock()">
                            <i class="fas fa-search me-1"></i>Check Stock
                        </button>
                    </div>
                </div>
            </div>
            
            <div id="stockResult" class="result-display" style="display: none;"></div>
        </div>

        <!-- Cart Validation -->
        <div class="integration-section">
            <h3><i class="fas fa-shopping-cart me-2 text-warning"></i>Shopping Cart Validation</h3>
            <p>Test validating an entire shopping cart before checkout</p>
            
            <div class="mb-3">
                <label class="form-label">Cart Items (JSON format)</label>
                <textarea class="form-control" id="cartItems" rows="6" placeholder="Enter cart items as JSON array">
[
  {"productId": "TSH_COTTON_BLK_M", "quantity": 2},
  {"productId": "MUG_CERAMIC_WHT_15OZ", "quantity": 1},
  {"productId": "BAG_TOTE_CAN_LRG", "quantity": 1}
]</textarea>
            </div>
            
            <button class="btn btn-warning" onclick="validateCart()">
                <i class="fas fa-check-circle me-1"></i>Validate Cart
            </button>
            <button class="btn btn-outline-warning ms-2" onclick="loadSampleCart()">
                <i class="fas fa-magic me-1"></i>Load Sample Cart
            </button>
            
            <div id="cartResult" class="result-display" style="display: none;"></div>
        </div>

        <!-- Order Submission -->
        <div class="integration-section">
            <h3><i class="fas fa-paper-plane me-2 text-primary"></i>Order Submission</h3>
            <p>Test submitting a completed order to ShopEasly Admin</p>
            
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">Order ID</label>
                    <input type="text" class="form-control" id="orderId" value="">
                </div>
                <div class="col-md-6">
                    <label class="form-label">Total Price</label>
                    <input type="number" class="form-control" id="orderTotal" value="67.48" step="0.01">
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-6">
                    <label class="form-label">Customer Name</label>
                    <input type="text" class="form-control" id="customerName" value="Jessica Martinez">
                </div>
                <div class="col-md-6">
                    <label class="form-label">Customer Email</label>
                    <input type="email" class="form-control" id="customerEmail" value="<EMAIL>">
                </div>
            </div>
            
            <div class="mt-3">
                <label class="form-label">Customer Address</label>
                <input type="text" class="form-control" id="customerAddress" value="1247 Sunset Boulevard, Los Angeles, CA 90026">
            </div>
            
            <div class="mt-3">
                <button class="btn btn-primary" onclick="submitOrder()">
                    <i class="fas fa-paper-plane me-1"></i>Submit Order
                </button>
                <button class="btn btn-outline-primary ms-2" onclick="generateOrderId()">
                    <i class="fas fa-refresh me-1"></i>Generate Order ID
                </button>
            </div>
            
            <div id="orderResult" class="result-display" style="display: none;"></div>
        </div>

        <!-- Order Status Tracking -->
        <div class="integration-section">
            <h3><i class="fas fa-truck me-2 text-info"></i>Order Status Tracking</h3>
            <p>Test checking order status for customer tracking pages</p>
            
            <div class="row">
                <div class="col-md-8">
                    <label class="form-label">Order ID to Track</label>
                    <input type="text" class="form-control" id="trackOrderId" placeholder="Enter order ID">
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button class="btn btn-info" onclick="trackOrder()">
                            <i class="fas fa-search me-1"></i>Track Order
                        </button>
                    </div>
                </div>
            </div>
            
            <div id="trackResult" class="result-display" style="display: none;"></div>
        </div>

        <!-- Integration Code Examples -->
        <div class="integration-section">
            <h3><i class="fas fa-code me-2 text-secondary"></i>Integration Code Examples</h3>
            <p>Copy these code snippets to integrate with your shopping website</p>
            
            <div class="accordion" id="codeAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#stockCheckCode">
                            Stock Check Integration
                        </button>
                    </h2>
                    <div id="stockCheckCode" class="accordion-collapse collapse" data-bs-parent="#codeAccordion">
                        <div class="accordion-body">
                            <pre><code>// Check stock before adding to cart
async function checkProductStock(productId, quantity) {
  const response = await fetch(
    `https://shop-easly-admin.onrender.com/api/production/inventory/check/${productId}?quantity=${quantity}`,
    {
      headers: {
        'X-API-Key': 'your-api-key-here'
      }
    }
  );
  
  const result = await response.json();
  return result.success ? result.data : null;
}

// Usage
const stock = await checkProductStock('TSH_COTTON_BLK_M', 2);
if (!stock.isInStock) {
  alert('Sorry, this item is out of stock');
}</code></pre>
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#orderSubmitCode">
                            Order Submission Integration
                        </button>
                    </h2>
                    <div id="orderSubmitCode" class="accordion-collapse collapse" data-bs-parent="#codeAccordion">
                        <div class="accordion-body">
                            <pre><code>// Submit order after payment success
async function submitOrderToAdmin(orderData) {
  const response = await fetch(
    'https://shop-easly-admin.onrender.com/api/production/orders',
    {
      method: 'POST',
      headers: {
        'X-API-Key': 'your-api-key-here',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        orderId: orderData.orderId,
        customerName: orderData.customer.name,
        customerEmail: orderData.customer.email,
        customerPhone: orderData.customer.phone,
        customerAddress: orderData.customer.address,
        items: orderData.items,
        totalPrice: orderData.total,
        paymentStatus: 'completed'
      })
    }
  );
  
  return response.json();
}</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let shopEaslyAPI = null;

        // Initialize API client
        function initializeAPI() {
            const apiKey = document.getElementById('apiKey').value;
            const adminUrl = document.getElementById('adminUrl').value;
            
            if (!apiKey) {
                alert('Please enter your API key');
                return false;
            }
            
            shopEaslyAPI = {
                apiKey: apiKey,
                baseUrl: adminUrl,
                
                async makeRequest(endpoint, options = {}) {
                    const url = `${this.baseUrl}/api/production${endpoint}`;
                    const config = {
                        headers: {
                            'X-API-Key': this.apiKey,
                            'Content-Type': 'application/json',
                            ...options.headers
                        },
                        ...options
                    };
                    
                    const response = await fetch(url, config);
                    const result = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${result.message || response.statusText}`);
                    }
                    
                    return result;
                }
            };
            
            return true;
        }

        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = `<div class="${type}">${content}</div>`;
        }

        async function testConnection() {
            if (!initializeAPI()) return;
            
            try {
                const response = await fetch(`${shopEaslyAPI.baseUrl}/api/production/health`);
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('connectionStatus').innerHTML = 
                        '<span class="badge bg-success">Connected ✓</span>';
                    showResult('connectionStatus', '', 'success');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                document.getElementById('connectionStatus').innerHTML = 
                    '<span class="badge bg-danger">Connection Failed ✗</span>';
                console.error('Connection test failed:', error);
            }
        }

        async function checkSingleStock() {
            if (!initializeAPI()) return;
            
            const productId = document.getElementById('stockProductId').value;
            const quantity = document.getElementById('stockQuantity').value;
            
            try {
                const result = await shopEaslyAPI.makeRequest(`/inventory/check/${productId}?quantity=${quantity}`);
                
                const stockStatus = result.data.stockStatus;
                const statusClass = stockStatus === 'in_stock' ? 'in-stock' : 
                                  stockStatus === 'low_stock' ? 'low-stock' : 'out-of-stock';
                
                showResult('stockResult', `
                    <strong>✅ Stock Check Result:</strong><br>
                    Product: ${result.data.productName}<br>
                    Available Stock: ${result.data.availableStock}<br>
                    Requested: ${result.data.requestedQuantity}<br>
                    Status: <span class="status-badge ${statusClass}">${stockStatus.replace('_', ' ').toUpperCase()}</span><br>
                    Can Fulfill: ${result.data.canFulfillOrder ? '✅ Yes' : '❌ No'}<br>
                    <br>
                    <strong>Full Response:</strong><br>
                    ${JSON.stringify(result.data, null, 2)}
                `, 'success');
                
            } catch (error) {
                showResult('stockResult', `❌ Stock check failed: ${error.message}`, 'error');
            }
        }

        async function validateCart() {
            if (!initializeAPI()) return;
            
            try {
                const cartItemsText = document.getElementById('cartItems').value;
                const cartItems = JSON.parse(cartItemsText);
                
                const result = await shopEaslyAPI.makeRequest('/inventory/bulk-check', {
                    method: 'POST',
                    body: JSON.stringify({ products: cartItems })
                });
                
                const productResults = result.data.products.map(p => {
                    const statusClass = p.isInStock ? 'in-stock' : 'out-of-stock';
                    return `${p.productId}: <span class="status-badge ${statusClass}">${p.isInStock ? 'IN STOCK' : 'OUT OF STOCK'}</span> (${p.availableStock} available)`;
                }).join('<br>');
                
                showResult('cartResult', `
                    <strong>${result.data.canFulfillEntireOrder ? '✅' : '❌'} Cart Validation Result:</strong><br>
                    Can Fulfill Entire Order: ${result.data.canFulfillEntireOrder ? 'YES' : 'NO'}<br>
                    <br>
                    <strong>Product Status:</strong><br>
                    ${productResults}<br>
                    <br>
                    <strong>Full Response:</strong><br>
                    ${JSON.stringify(result.data, null, 2)}
                `, result.data.canFulfillEntireOrder ? 'success' : 'warning');
                
            } catch (error) {
                showResult('cartResult', `❌ Cart validation failed: ${error.message}`, 'error');
            }
        }

        async function submitOrder() {
            if (!initializeAPI()) return;
            
            try {
                const orderData = {
                    orderId: document.getElementById('orderId').value,
                    customerName: document.getElementById('customerName').value,
                    customerEmail: document.getElementById('customerEmail').value,
                    customerAddress: document.getElementById('customerAddress').value,
                    items: [
                        {
                            productId: "TSH_COTTON_BLK_M",
                            productName: "Premium Cotton T-Shirt - Black - Medium",
                            quantity: 2,
                            price: 24.99
                        },
                        {
                            productId: "MUG_CERAMIC_WHT_15OZ",
                            productName: "Ceramic Coffee Mug - White - 15oz",
                            quantity: 1,
                            price: 12.50
                        }
                    ],
                    totalPrice: parseFloat(document.getElementById('orderTotal').value),
                    paymentStatus: 'completed',
                    shippingMethod: 'Standard Ground'
                };
                
                const result = await shopEaslyAPI.makeRequest('/orders', {
                    method: 'POST',
                    body: JSON.stringify(orderData)
                });
                
                // Update track order field with submitted order ID
                document.getElementById('trackOrderId').value = orderData.orderId;
                
                showResult('orderResult', `
                    <strong>✅ Order Submitted Successfully!</strong><br>
                    Order ID: ${result.data.orderId}<br>
                    Status: ${result.data.status}<br>
                    Message: ${result.data.message}<br>
                    <br>
                    <strong>Full Response:</strong><br>
                    ${JSON.stringify(result.data, null, 2)}
                `, 'success');
                
            } catch (error) {
                showResult('orderResult', `❌ Order submission failed: ${error.message}`, 'error');
            }
        }

        async function trackOrder() {
            if (!initializeAPI()) return;
            
            const orderId = document.getElementById('trackOrderId').value;
            if (!orderId) {
                alert('Please enter an Order ID to track');
                return;
            }
            
            try {
                const result = await shopEaslyAPI.makeRequest(`/orders/${orderId}/status`);
                
                showResult('trackResult', `
                    <strong>✅ Order Status Retrieved:</strong><br>
                    Order ID: ${result.data.orderId}<br>
                    Status: <span class="badge bg-primary">${result.data.status.toUpperCase()}</span><br>
                    ${result.data.trackingNumber ? `Tracking Number: ${result.data.trackingNumber}<br>` : ''}
                    ${result.data.estimatedDelivery ? `Estimated Delivery: ${new Date(result.data.estimatedDelivery).toLocaleDateString()}<br>` : ''}
                    Last Updated: ${new Date(result.data.lastUpdated).toLocaleString()}<br>
                    <br>
                    <strong>Full Response:</strong><br>
                    ${JSON.stringify(result.data, null, 2)}
                `, 'success');
                
            } catch (error) {
                showResult('trackResult', `❌ Order tracking failed: ${error.message}`, 'error');
            }
        }

        function loadSampleCart() {
            document.getElementById('cartItems').value = JSON.stringify([
                {"productId": "TSH_COTTON_BLK_M", "quantity": 2},
                {"productId": "MUG_CERAMIC_WHT_15OZ", "quantity": 1},
                {"productId": "BAG_TOTE_CAN_LRG", "quantity": 1}
            ], null, 2);
        }

        function generateOrderId() {
            const orderId = 'WEB_' + Date.now();
            document.getElementById('orderId').value = orderId;
        }

        // Initialize with sample data
        generateOrderId();
        loadSampleCart();
    </script>
</body>
</html>
